/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.quotation-card.data-v-c7b08e0d {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  transition: all 0.3s ease;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
}
.quotation-card .card-type-tag.data-v-c7b08e0d {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
}
.quotation-card .card-type-tag.data-v-c7b08e0d .wd-tag {
  font-size: 20rpx;
  border-radius: 8rpx 0 8rpx 8rpx;
  padding: 4rpx 12rpx;
  border-top-right-radius: 20rpx;
  font-weight: 500;
}
.quotation-card .card-type-tag.data-v-c7b08e0d .wd-tag[data-type=primary] {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}
.quotation-card .card-type-tag.data-v-c7b08e0d .wd-tag[data-type=danger] {
  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%) !important;
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
}
.quotation-card.data-v-c7b08e0d:hover {
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
  transform: translateY(-4rpx);
  border-color: rgba(102, 126, 234, 0.2);
}
.quotation-card.data-v-c7b08e0d:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
.quotation-card .card-body.data-v-c7b08e0d {
  display: flex;
  align-items: stretch;
  gap: 24rpx;
  width: 100%;
}
.quotation-card .card-body .left-content.data-v-c7b08e0d {
  flex: 2;
  min-width: 0; /* 关键：允许flex子项收缩 */
  max-width: 66.666%; /* 强制限制最大宽度为2/3 */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 120rpx;
  overflow: hidden; /* 防止内容溢出 */
  box-sizing: border-box;
}
.quotation-card .card-body .left-content .quotation-title.data-v-c7b08e0d {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.4;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%; /* 确保标题不超过容器宽度 */
}
.quotation-card .card-body .left-content .publisher-info.data-v-c7b08e0d {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  margin-bottom: 16rpx;
  min-width: 0; /* 允许收缩 */
}
.quotation-card .card-body .left-content .publisher-info .publisher-name.data-v-c7b08e0d {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  min-width: 0;
}
.quotation-card .card-body .left-content .publisher-info .remaining-time.data-v-c7b08e0d {
  font-size: 24rpx;
  color: #52c41a;
  padding: 4rpx 8rpx;
  background: rgba(82, 196, 26, 0.1);
  border-radius: 8rpx;
  flex-shrink: 0; /* 不允许收缩 */
}
.quotation-card .card-body .left-content .publisher-info .remaining-time.expired.data-v-c7b08e0d {
  color: #ff4d4f;
  background: rgba(255, 77, 79, 0.1);
}
.quotation-card .card-body .left-content .tag-info.data-v-c7b08e0d {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex-wrap: nowrap; /* 改为不换行，防止高度增加 */
  min-width: 0; /* 允许收缩 */
  overflow: hidden; /* 防止标签溢出 */
  width: 100%; /* 确保不超过容器宽度 */
}
.quotation-card .card-body .left-content .tag-info.data-v-c7b08e0d .wd-tag {
  font-size: 22rpx;
  border-radius: 12rpx;
  padding: 4rpx 12rpx;
  flex-shrink: 1; /* 允许标签收缩 */
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 120rpx; /* 限制单个标签最大宽度 */
}
.quotation-card .card-body .right-content.data-v-c7b08e0d {
  flex: 1;
  min-width: 0; /* 允许收缩 */
  max-width: 33.333%; /* 强制限制最大宽度为1/3 */
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center; /* 居中对齐 */
  min-height: 120rpx;
  overflow: hidden; /* 防止内容溢出 */
}
.quotation-card .card-body .right-content .price-display.data-v-c7b08e0d {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center; /* 改为居中对齐 */
  text-align: center; /* 改为居中对齐 */
  padding: 12rpx 0;
  min-width: 0; /* 允许收缩 */
  overflow: hidden; /* 防止内容溢出 */
  height: 80rpx; /* 设置固定高度，确保跨卡片对齐 */
}
.quotation-card .card-body .right-content .price-display .price-value.data-v-c7b08e0d {
  font-weight: 900;
  color: #1890ff;
  line-height: 1;
  text-shadow: 0 2rpx 4rpx rgba(24, 144, 255, 0.15);
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  transition: font-size 0.3s ease; /* 字体大小变化动画 */
}
.quotation-card .card-body .right-content .price-display .basis-display.data-v-c7b08e0d {
  display: flex;
  flex-direction: column;
  align-items: center; /* 改为水平居中 */
  justify-content: center; /* 保持垂直居中 */
  gap: 6rpx; /* 减少间距，让整体更紧凑 */
  min-width: 0; /* 允许收缩 */
  overflow: hidden; /* 防止内容溢出 */
  width: 100%;
  height: 100%; /* 占满price-display的高度 */
}
.quotation-card .card-body .right-content .price-display .basis-display .contract-name.data-v-c7b08e0d {
  font-size: 26rpx; /* 稍微减小字体 */
  color: #595959;
  font-weight: 700;
  letter-spacing: 2rpx;
  text-transform: uppercase;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  text-align: center; /* 文字居中 */
}
.quotation-card .card-body .right-content .price-display .basis-display .basis-value.data-v-c7b08e0d {
  font-weight: 900;
  line-height: 1;
  text-shadow: 0 2rpx 4rpx rgba(255, 77, 79, 0.15);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  text-align: center; /* 文字居中 */
  transition: font-size 0.3s ease; /* 字体大小变化动画 */
}
.quotation-card .card-body .right-content .price-display .basis-display .basis-value.data-v-c7b08e0d:first-letter {
  font-size: 36rpx;
}
.quotation-card .card-body .right-content .price-display .basis-display .basis-value[data-positive=true].data-v-c7b08e0d {
  color: #ff4d4f;
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.quotation-card .card-body .right-content .price-display .basis-display .basis-value[data-negative=true].data-v-c7b08e0d {
  color: #52c41a;
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.adaptive-price.data-v-c7b08e0d {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.adaptive-price.data-v-c7b08e0d:hover {
  position: relative;
  z-index: 100;
}
.adaptive-price.data-v-c7b08e0d:hover::after {
  content: attr(title);
  position: absolute;
  top: -40rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  white-space: nowrap;
  z-index: 101;
  pointer-events: none;
  opacity: 0;
  animation: fadeIn-c7b08e0d 0.3s ease forwards;
}
@keyframes fadeIn-c7b08e0d {
from {
    opacity: 0;
    transform: translateX(-50%) translateY(-5rpx);
}
to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
}
}
@media (max-width: 750rpx) {
.adaptive-price.price-value.data-v-c7b08e0d {
    font-size: clamp(24rpx, 5vw, 40rpx) !important;
}
.adaptive-price.basis-value.data-v-c7b08e0d {
    font-size: clamp(22rpx, 4.5vw, 36rpx) !important;
}
}