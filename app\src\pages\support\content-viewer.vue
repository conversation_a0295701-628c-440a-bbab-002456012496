<route lang="json">
{
  "style": {
    "navigationBarTitleText": "内容详情"
  }
}
</route>

<template>
  <view class="content-viewer">
    <view v-if="loading" class="loading-container">
      <wd-loading />
      <text class="loading-text">加载中...</text>
    </view>

    <view v-else-if="error" class="error-container">
      <wd-icon name="warning" size="64rpx" color="#f56c6c" />
      <text class="error-text">{{ error }}</text>
      <wd-button type="primary" size="small" @click="loadContent">重试</wd-button>
    </view>

    <scroll-view v-else class="content-scroll" scroll-y>
      <view class="content-body">
        <zero-markdown-view :markdown="markdownContent" />
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { toast } from '@/utils/toast'

// 页面参数
interface PageParams {
  key?: string
  title?: string
}

const loading = ref(true)
const error = ref('')
const markdownContent = ref('')
const contentKey = ref('')
const pageTitle = ref('内容详情')

// 只保留基本的markdown内容
// 其他搜索和目录相关变量已删除

// 内容映射
const contentMap: Record<string, { title: string; content: string }> = {
  help: {
    title: '帮助中心',
    content: `# 帮助中心

欢迎使用**点价交易平台**！这里为您提供详细的使用指南和常见问题解答。

## 🚀 快速开始

### 如何注册账户？

1. 点击登录页面的**"注册"**按钮
2. 填写手机号码并获取验证码
3. 设置安全的登录密码
4. 完善个人或企业信息

> **提示**：建议使用真实信息注册，有助于提升账户安全性和交易信誉。

### 如何进行交易？

1. **浏览商品**：在首页浏览商品列表或使用搜索功能
2. **查看详情**：点击商品查看详细信息和实时价格
3. **选择交易方式**：支持现货交易、期货交易等多种方式
4. **确认订单**：核对交易信息并完成支付

---

## 📞 联系客服

如果您在使用过程中遇到任何问题，可以通过以下方式联系我们：

- **客服热线**：400-123-4567
- **工作时间**：周一至周五 9:00-18:00
- **在线客服**：点击右下角客服图标
- **邮箱支持**：<EMAIL>

## 🔧 功能指南

### 账户管理
- 修改个人信息和头像
- 设置安全密码
- 企业认证流程
- 实名认证指南

### 交易功能
- 下单流程详解
- 支付方式说明
- 订单状态查询
- 交易记录查看

### 安全须知

> ⚠️ **重要提醒**：请妥善保管您的账户信息，不要向他人透露密码。

- 定期更换密码
- 开启双重验证
- 识别诈骗信息
- 安全交易建议

## 💡 常见问题

**Q: 忘记密码怎么办？**
A: 点击登录页面的"忘记密码"，通过手机验证码重置密码。

**Q: 如何修改绑定手机号？**
A: 进入"账户中心" → "安全设置" → "修改手机号"。

**Q: 交易出现问题如何处理？**
A: 请及时联系客服，我们会在第一时间为您处理。`
  },
  privacy: {
    title: '隐私政策',
    content: `# 隐私政策

**生效日期：2024年1月1日**

感谢您使用点价交易平台。我们深知个人信息对您的重要性，并会尽全力保护您的个人信息安全可靠。

---

## 📋 信息收集

### 个人基本信息
我们可能收集以下类型的个人信息：

- **身份信息**：姓名、身份证号、手机号码、邮箱地址
- **企业信息**：企业名称、营业执照、法人信息
- **交易信息**：交易记录、支付信息、偏好设置
- **联系信息**：通讯地址、紧急联系人

### 设备与技术信息
- 设备型号、操作系统版本
- 网络信息、IP地址、MAC地址
- 应用使用统计数据、崩溃日志

> **说明**：我们仅收集为您提供服务所必需的信息。

## 🎯 信息使用

我们使用收集的信息用于：

1. **服务提供**：注册账户、身份验证、交易处理
2. **服务改进**：分析用户行为、优化产品功能
3. **客户支持**：处理咨询、解决问题、提供帮助
4. **安全保障**：风险控制、欺诈防范、账户安全

## 🔒 信息保护

### 安全措施
我们采用行业领先的安全技术保护您的信息：

- **传输加密**：采用SSL/TLS加密传输
- **存储加密**：敏感数据加密存储
- **访问控制**：严格的权限管理制度
- **安全审计**：定期进行安全评估

### 共享限制
我们**不会**向第三方出售、交易或转让您的个人信息，除非：

- 获得您的**明确同意**
- **法律法规**明确要求
- 保护我们的**合法权利**和安全

## ⚖️ 您的权利

根据相关法律法规，您享有以下权利：

- **知情权**：了解个人信息处理情况
- **访问权**：查看我们持有的您的个人信息
- **更正权**：要求更正不准确的个人信息
- **删除权**：要求删除个人信息
- **限制权**：限制个人信息的处理
- **携带权**：获取个人信息副本

## 📞 联系我们

如有隐私相关问题或需要行使您的权利，请通过以下方式联系我们：

- **邮箱**：<EMAIL>
- **电话**：400-123-4567
- **地址**：上海市浦东新区XX路XX号

我们将在**15个工作日**内回复您的请求。`
  },
  terms: {
    title: '服务条款',
    content: `
# 服务条款

**生效日期：2024年1月1日**

## 1. 服务说明

### 1.1 服务内容
本平台为用户提供商品交易信息服务，包括但不限于：
- 商品信息展示
- 价格查询服务
- 交易撮合服务
- 相关咨询服务

### 1.2 服务范围
服务覆盖钢铁、有色金属、化工等大宗商品领域。

## 2. 用户义务

### 2.1 注册义务
- 提供真实、准确的注册信息
- 及时更新个人或企业信息
- 保护账户安全

### 2.2 使用义务
- 遵守法律法规
- 不得从事违法违规活动
- 不得损害他人合法权益

## 3. 平台责任

### 3.1 服务提供
我们承诺：
- 提供稳定的服务
- 保护用户信息安全
- 及时处理用户反馈

### 3.2 责任限制
平台不承担因以下原因造成的损失：
- 不可抗力因素
- 用户操作失误
- 第三方原因

## 4. 知识产权

### 4.1 平台权利
平台拥有网站设计、软件、商标等知识产权。

### 4.2 用户权利
用户发布的内容，用户保留相应权利。

## 5. 争议解决

### 5.1 协商解决
双方应首先通过友好协商解决争议。

### 5.2 法律途径
协商不成的，可向有管辖权的人民法院提起诉讼。

## 6. 条款修改

我们保留修改本条款的权利，修改后将及时通知用户。

## 7. 联系方式

如有疑问，请联系：
- 邮箱：<EMAIL>
- 电话：400-123-4567
    `
  }
}



// 所有搜索和目录相关函数已删除

// 所有搜索和目录功能已删除，只保留基本的markdown渲染

// 加载内容
const loadContent = async () => {
  loading.value = true
  error.value = ''
  
  try {
    const content = contentMap[contentKey.value]
    if (!content) {
      throw new Error('内容不存在')
    }
    
    // 设置页面标题
    pageTitle.value = content.title
    uni.setNavigationBarTitle({
      title: content.title
    })
    
    // 设置Markdown内容
    markdownContent.value = content.content
    
  } catch (err: any) {
    console.error('加载内容失败:', err)
    error.value = err.message || '加载失败，请重试'
  } finally {
    loading.value = false
  }
}

// 页面加载
onLoad((options: PageParams) => {
  console.log('页面参数:', options)
  contentKey.value = options.key || 'help'
  if (options.title) {
    pageTitle.value = options.title
  }
})

onMounted(() => {
  loadContent()
})
</script>

<style lang="scss" scoped>
.content-viewer {
  height: 100vh;
  background: #f8f9fa;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  
  .loading-text {
    margin-top: 24rpx;
    font-size: 28rpx;
    color: #909399;
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 40rpx;
  
  .error-text {
    margin: 24rpx 0 32rpx 0;
    font-size: 28rpx;
    color: #f56c6c;
    text-align: center;
  }
}

.content-scroll {
  height: 100vh;
}

// 删除了所有搜索和目录相关的样式

.content-body {
  background: #fff;
  min-height: 100%;
  padding: 32rpx 24rpx;
}

// 所有目录相关样式已删除


</style>
