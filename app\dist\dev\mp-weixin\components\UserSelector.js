"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../common/vendor.js");
const api_user = require("../api/user.js");
const utils_toast = require("../utils/toast.js");
if (!Array) {
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_wd_search2 = common_vendor.resolveComponent("wd-search");
  const _easycom_wd_loading2 = common_vendor.resolveComponent("wd-loading");
  const _easycom_wd_radio2 = common_vendor.resolveComponent("wd-radio");
  const _easycom_wd_popup2 = common_vendor.resolveComponent("wd-popup");
  (_easycom_wd_button2 + _easycom_wd_search2 + _easycom_wd_loading2 + _easycom_wd_radio2 + _easycom_wd_popup2)();
}
const _easycom_wd_button = () => "../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_wd_search = () => "../node-modules/wot-design-uni/components/wd-search/wd-search.js";
const _easycom_wd_loading = () => "../node-modules/wot-design-uni/components/wd-loading/wd-loading.js";
const _easycom_wd_radio = () => "../node-modules/wot-design-uni/components/wd-radio/wd-radio.js";
const _easycom_wd_popup = () => "../node-modules/wot-design-uni/components/wd-popup/wd-popup.js";
if (!Math) {
  (_easycom_wd_button + _easycom_wd_search + _easycom_wd_loading + _easycom_wd_radio + _easycom_wd_popup)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "UserSelector",
  props: {
    modelValue: { default: 0 },
    label: { default: "用户选择" },
    placeholder: { default: "请选择用户" },
    customClass: { default: "" },
    customLabelClass: { default: "" },
    customValueClass: { default: "" }
  },
  emits: ["update:modelValue", "change"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const showModal = common_vendor.ref(false);
    const searchKeyword = common_vendor.ref("");
    const selectedUser = common_vendor.ref(null);
    const availableUsers = common_vendor.ref([]);
    const isLoading = common_vendor.ref(false);
    const currentUserId = common_vendor.computed({
      get: () => props.modelValue,
      set: (value) => {
        emit("update:modelValue", value);
      }
    });
    function loadAvailableUsers() {
      return __async(this, null, function* () {
        try {
          isLoading.value = true;
          const res = yield api_user.getSelectableList({
            search: searchKeyword.value || void 0,
            page: 1,
            pageSize: 200
            // 测试阶段：一次性获取所有用户
          });
          availableUsers.value = res.data.list;
        } catch (error) {
          console.error("获取用户列表失败:", error);
          utils_toast.toast.error("获取用户列表失败");
          availableUsers.value = [];
        } finally {
          isLoading.value = false;
        }
      });
    }
    function showUserPicker() {
      loadAvailableUsers();
      showModal.value = true;
    }
    function searchUsers() {
      loadAvailableUsers();
    }
    function confirmUserSelection(user) {
      selectedUser.value = user;
      currentUserId.value = user.ID;
      showModal.value = false;
      emit("change", user);
    }
    function removeUser() {
      selectedUser.value = null;
      currentUserId.value = 0;
      emit("change", null);
    }
    function initSelectedUser() {
      return __async(this, null, function* () {
        if (props.modelValue && props.modelValue > 0) {
          try {
            const res = yield api_user.getSelectableProfile(props.modelValue);
            selectedUser.value = res.data;
          } catch (error) {
            console.error("获取用户信息失败:", error);
            selectedUser.value = null;
          }
        }
      });
    }
    common_vendor.watch(() => props.modelValue, (newValue) => __async(this, null, function* () {
      if (newValue && newValue > 0 && (!selectedUser.value || selectedUser.value.ID !== newValue)) {
        yield initSelectedUser();
      } else if (!newValue || newValue === 0) {
        selectedUser.value = null;
      }
    }), { immediate: true });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: !selectedUser.value
      }, !selectedUser.value ? {
        b: common_vendor.t(_ctx.placeholder)
      } : {
        c: common_vendor.t(selectedUser.value.nickName),
        d: common_vendor.t(selectedUser.value.phone)
      }, {
        e: !selectedUser.value
      }, !selectedUser.value ? {
        f: common_vendor.o(showUserPicker),
        g: common_vendor.p({
          type: "primary",
          size: "small",
          ["custom-class"]: "dj-btn-primary"
        })
      } : {
        h: common_vendor.o(removeUser),
        i: common_vendor.p({
          type: "error",
          size: "small",
          ["custom-class"]: "dj-btn-danger"
        })
      }, {
        j: common_vendor.t(_ctx.label),
        k: common_vendor.o(searchUsers),
        l: common_vendor.o(($event) => searchKeyword.value = $event),
        m: common_vendor.p({
          placeholder: "搜索用户",
          ["custom-class"]: "dj-search",
          modelValue: searchKeyword.value
        }),
        n: isLoading.value
      }, isLoading.value ? {
        o: common_vendor.p({
          type: "ring",
          color: "#667eea"
        })
      } : availableUsers.value.length === 0 ? {} : {
        q: common_vendor.f(availableUsers.value, (user, k0, i0) => {
          var _a;
          return {
            a: common_vendor.t(user.nickName),
            b: common_vendor.t(user.phone),
            c: "d36d9223-5-" + i0 + ",d36d9223-2",
            d: common_vendor.p({
              value: ((_a = selectedUser.value) == null ? void 0 : _a.ID) === user.ID,
              ["custom-class"]: "dj-radio"
            }),
            e: user.ID,
            f: common_vendor.o(($event) => confirmUserSelection(user), user.ID)
          };
        })
      }, {
        p: availableUsers.value.length === 0,
        r: common_vendor.o(($event) => showModal.value = $event),
        s: common_vendor.p({
          position: "bottom",
          ["custom-style"]: "height: 70%",
          ["custom-class"]: "dj-popup",
          modelValue: showModal.value
        }),
        t: common_vendor.n(_ctx.customClass)
      });
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d36d9223"]]);
wx.createComponent(Component);
