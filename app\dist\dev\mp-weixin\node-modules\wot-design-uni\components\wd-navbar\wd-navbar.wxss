/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 混合宏
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
  * BEM，定义块（b)
  */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 此方法用于生成穿透样式 */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 定义状态（m） */
/* 定义状态（m） */
/* 对于需要需要嵌套在 m 底下的 e，调用这个混合宏，一般在切换整个组件的状态，如切换颜色的时候 */
/* 状态，生成 is-$state 类名 */
/**
  * 常用混合宏
  */
/* 单行超出隐藏 */
/* 多行超出隐藏 */
/* 清除浮动 */
/* 0.5px 边框 指定方向*/
/* 0.5px 边框 环绕 */
/**
  * 三角形实现尖角样式，适用于背景透明情况
  * @param $size 三角形高，底边为 $size * 2
  * @param $bg 三角形背景颜色
  */
/**
  * 正方形实现尖角样式，适用于背景不透明情况
  * @param $size 正方形边长
  * @param $bg 正方形背景颜色
  * @param $z-index z-index属性值，不得大于外部包裹器
  * @param $box-shadow 阴影
*/
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
 * UI规范基础变量
 */
/*----------------------------------------- Theme color. start ----------------------------------------*/
/* 主题颜色 */
/* 辅助色 */
/* 文字颜色（默认浅色背景下 */
/* 暗黑模式 */
/* 图形颜色 */
/*----------------------------------------- Theme color. end -------------------------------------------*/
/*-------------------------------- Theme color application size.  start --------------------------------*/
/* 文字字号 */
/* 文字字重 */
/* 尺寸 */
/*-------------------------------- Theme color application size.  end --------------------------------*/
/* component var */
/* action-sheet */
/* badge */
/* button */
/* cell */
/* calendar */
/* checkbox */
/* collapse */
/* divider */
/* drop-menu */
/* input-number */
/* input */
/* textarea */
/* loadmore */
/* message-box */
/* notice-bar */
/* pagination */
/* picker */
/* col-picker */
/* overlay */
/* popup */
/* progress */
/* radio */
/* search */
/* slider */
/* sort-button */
/* steps */
/* switch */
/* tabs */
/* tag */
/* toast */
/* loading */
/* tooltip */
/* popover */
/* grid-item */
/* statustip */
/* card */
/* upload */
/* curtain */
/* notify */
/* skeleton */
/* circle */
/* swiper */
/* swiper-nav */
/* segmented */
/* tabbar */
/* tabbar-item */
/* navbar */
/* navbar-capsule */
/* table */
/* sidebar */
/* sidebar-item */
/* fab */
/* count-down */
/* keyboard */
/* number-keyboard */
/* passwod-input */
/* form-item */
/* backtop */
/* index-bar */
/* text */
/* video-preview */
/* img-cropper */
/* floating-panel */
/* signature */
.wot-theme-dark .wd-navbar {
  background-color: var(--wot-dark-background, #131313);
}
.wot-theme-dark .wd-navbar__title {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wot-theme-dark .wd-navbar__text {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wot-theme-dark .wd-navbar .wd-navbar__arrow {
  color: var(--wot-dark-color, var(--wot-color-white, rgb(255, 255, 255)));
}
.wd-navbar {
  position: relative;
  text-align: center;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  height: var(--wot-navbar-height, 44px);
  line-height: var(--wot-navbar-height, 44px);
  background-color: var(--wot-navbar-background, var(--wot-color-white, rgb(255, 255, 255)));
  box-sizing: content-box;
}
.wd-navbar__content {
  position: relative;
  height: 100%;
  width: 100%;
}
.wd-navbar__title {
  max-width: 60%;
  height: 100%;
  margin: 0 auto;
  color: var(--wot-navbar-color, var(--wot-font-gray-1, rgba(0, 0, 0, 0.9)));
  font-weight: var(--wot-navbar-title-font-weight, 600);
  font-size: var(--wot-navbar-title-font-size, 18px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.wd-navbar__text {
  display: inline-block;
  vertical-align: middle;
  color: var(--wot-navbar-desc-font-color, var(--wot-font-gray-1, rgba(0, 0, 0, 0.9)));
}
.wd-navbar__left, .wd-navbar__right, .wd-navbar__capsule {
  position: absolute;
  top: 0;
  bottom: 0;
  font-size: var(--wot-navbar-desc-font-size, 16px);
  display: flex;
  align-items: center;
  padding: 0 12px;
}
.wd-navbar__left.is-disabled, .wd-navbar__right.is-disabled, .wd-navbar__capsule.is-disabled {
  opacity: var(--wot-navbar-disabled-opacity, 0.6);
}
.wd-navbar__left, .wd-navbar__capsule {
  left: 0;
}
.wd-navbar__right {
  right: 0;
}
  .wd-navbar__arrow {
  font-size: var(--wot-navbar-arrow-size, 24px);
  color: var(--wot-navbar-color, var(--wot-font-gray-1, rgba(0, 0, 0, 0.9)));
}
.wd-navbar.is-border {
  position: relative;
}
.wd-navbar.is-border::after {
  position: absolute;
  display: block;
  content: "";
  width: 100%;
  height: 1px;
  left: 0;
  bottom: 0;
  transform: scaleY(0.5);
  background: var(--wot-color-border-light, #e8e8e8);
}
.wd-navbar.is-fixed {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 500;
}