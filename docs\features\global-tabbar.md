# 功能名称：全局统一底部导航栏 (Global Unified Bottom Tabbar)

## 1. 功能简介

本项目旨在设计并实现一个全局统一的底部 Tabbar 组件。该组件将作为应用核心功能模块（如工作台、报价市场、我的）的主要导航入口，确保在 H5、小程序、App 等多端环境下提供一致、流畅的视觉和交互体验。

我们将采用 `wot-design-uni` UI 组件库中的 `wd-tabbar` 组件作为基础，通过 Uni-app 的自定义 Tabbar 功能进行实现，以达到高度的可定制性和统一的维护性。

## 2. 数据定义

为了集中管理 Tabbar 的内容和路由，我们定义一个标准化的数据结构来描述每个标签页。该配置将作为组件的数据源。

**Tabbar 配置数组 (`tabbarList.ts`)**

建议在 `src/config` 或类似目录下创建一个 `tabbarList.ts` 文件来存放此配置。

```typescript
// src/config/tabbarList.ts
export interface TabbarItem {
  name: string; // 唯一标识符，对应 wd-tabbar-item 的 name
  text: string; // 显示的文本
  icon: string; // wot-design-uni 的图标名称
  pagePath: string; // 对应的页面路径
}

export const tabbarList: TabbarItem[] = [
  {
    name: 'workbench',
    text: '工作台',
    icon: 'home',
    pagePath: '/pages/workbench/index'
  },
  {
    name: 'quotation',
    text: '报价市场',
    icon: 'cart',
    pagePath: '/pages/quotation/index'
  },
  {
    name: 'profile',
    text: '我的',
    icon: 'user',
    pagePath: '/pages/my/index'
  }
];
```

## 3. 子功能详细设计

### 步骤一：环境与依赖准备

1.  **确认 `wot-design-uni` 已安装**
    如果项目中尚未安装，请执行以下命令：
    ```shell
    pnpm add wot-design-uni
    ```

2.  **在 `main.ts` 中引入组件库**
    确保 `wot-design-uni` 的样式和配置已在项目入口文件 `src/main.ts` 中正确引入。
    ```typescript
    // src/main.ts
    import { createSSRApp } from 'vue';
    import App from './App.vue';
    // 引入 wot-design-uni 样式
    import 'wot-design-uni/dist/style.css';

    export function createApp() {
      const app = createSSRApp(App);
      return {
        app,
      };
    }
    ```

### 步骤二：创建自定义 Tabbar 组件

在 `src/components` 目录下创建一个用于承载自定义 Tabbar 的组件。

**文件路径**: `src/components/layout/custom-tabbar/index.vue`

**组件代码**:

```vue
<template>
  <wd-tabbar
    v-model="active"
    fixed
    bordered
    @change="handleChange"
  >
    <wd-tabbar-item
      v-for="item in list"
      :key="item.name"
      :name="item.name"
      :title="item.text"
      :icon="item.icon"
    />
  </wd-tabbar>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import { tabbarList, TabbarItem } from '@/config/tabbarList';

const list = ref(tabbarList);

// 计算当前激活的 tab
const active = computed(() => {
  const pages = getCurrentPages();
  if (pages.length === 0) return '';
  const currentPage = pages[pages.length - 1];
  const route = currentPage.route;
  const currentTab = list.value.find(item => `/${route}` === item.pagePath);
  return currentTab ? currentTab.name : '';
});

// 切换 tab
const handleChange = ({ value }: { value: string }) => {
  const targetTab = list.value.find(item => item.name === value);
  if (targetTab && targetTab.pagePath) {
    uni.switchTab({
      url: targetTab.pagePath
    });
  }
};

// onShow 钩子确保每次页面显示时都能正确更新 tabbar 状态
// (虽然 computed 属性已经处理了大部分情况，但 onShow 是一个可靠的补充)
onShow(() => {
  // 可以在这里添加需要每次显示时都执行的逻辑，例如获取角标数量
});
</script>

<style lang="scss" scoped>
// 可在此处添加自定义样式覆盖
</style>
```

### 步骤三：配置 `pages.config.ts`

修改项目根目录下的 `pages.config.ts`，启用自定义 Tabbar 并指定其路径和页面列表。

```typescript
// pages.config.ts
import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages';

export default defineUniPages({
  // ... 其他配置
  tabBar: {
    custom: true, // 启用自定义 Tabbar
    color: '#8A8A8A',
    selectedColor: '#2F85FC',
    backgroundColor: '#FFFFFF',
    borderStyle: 'black',
    list: [
      {
        pagePath: 'pages/workbench/index',
        text: '工作台'
      },
      {
        pagePath: 'pages/quotation/index',
        text: '报价市场'
      },
      {
        pagePath: 'pages/my/index',
        text: '我的'
      }
    ],
    // Uni-app 编译时会忽略这里的 iconPath, selectedIconPath, text
    // 但保留它们有助于开发者理解页面结构
  },
  // ... 其他配置
});
```
**重要**: `list` 数组中的 `pagePath` 必须与 `tabbarList.ts` 中定义的路径完全对应。

### 步骤四：处理页面内容遮挡问题

由于自定义 Tabbar 是一个 `fixed` 定位的组件，它会浮在页面内容之上，可能遮挡页面底部的元素。所有作为 Tabbar 的页面都需要在根元素上增加一个 `padding-bottom` 来为 Tabbar 预留空间。

**方案**:
1.  测量自定义 Tabbar 的实际高度（通常在 50px 到 60px 之间）。
2.  创建一个全局样式类，或在 `App.vue` 的全局样式中定义。

```css
/* App.vue 或全局样式文件 */
.page-container-with-tabbar {
  /* 假设 tabbar 高度为 55px，额外增加 10px 间距 */
  padding-bottom: 65px;
  box-sizing: border-box;
}
```
3.  在所有 Tabbar 页面（工作台、报价市场、我的）的根 `view` 上应用这个类。

```vue
<!-- 例如：pages/workbench/index.vue -->
<template>
  <view class="workbench-page page-container-with-tabbar">
    <!-- 页面内容 -->
  </view>
</template>
```

## 4. 接口定义

**`custom-tabbar/index.vue` 组件**

*   **Props**: 无
*   **Events**: 无（事件处理在组件内部完成，通过 `uni.switchTab` 与应用其他部分交互）
*   **依赖**: `wot-design-uni` 的 `wd-tabbar` 和 `wd-tabbar-item` 组件。

## 5. 相关页面

*   `/pages/workbench/index.vue`
*   `/pages/quotation/index.vue`
*   `/pages/my/index.vue`
*   `/src/components/layout/custom-tabbar/index.vue` (本次新建)
*   `/pages.config.ts` (本次修改)
*   `/src/config/tabbarList.ts` (本次新建)

## 6. 测试用例

| 测试项 | 测试步骤 | 预期结果 | 平台 |
| :--- | :--- | :--- | :--- |
| **基础渲染** | 1. 进入任意一个 Tabbar 页面（如工作台）。 | 1. 页面底部正确显示 Tabbar。 <br> 2. Tabbar 上有“工作台”、“报价市场”、“我的”三个标签。 <br> 3. “工作台”标签处于高亮选中状态。 | H5, 小程序, App |
| **导航功能** | 1. 在工作台页面，点击“报价市场”标签。 <br> 2. 再点击“我的”标签。 | 1. 页面成功跳转到报价市场页面，且“报价市场”标签高亮。 <br> 2. 页面成功跳转到我的页面，且“我的”标签高亮。 | H5, 小程序, App |
| **状态持久化** | 1. 从“我的”页面切换到“工作台”。 <br> 2. 刷新页面（H5）或退出重进（小程序）。 | 1. 页面仍停留在工作台页面，且“工作台”标签高亮。 | H5, 小程序 |
| **非 Tabbar 页面** | 1. 从工作台页面进入一个详情页（非 Tabbar 页面）。 | 1. 底部 Tabbar 消失。 | H5, 小程序, App |
| **内容避让** | 1. 在每个 Tabbar 页面滚动到最底部。 | 1. 页面最下方的所有内容都不会被 Tabbar 遮挡。 | H5, 小程序, App |

## 7. 注意事项

1.  **内容遮挡**：必须为所有 Tabbar 页面添加 `padding-bottom`，否则页面底部内容会被遮挡。此距离需要精确测量和调整。
2.  **图标库**：方案中使用的 `'home'`, `'cart'`, `'user'` 是 `wot-design-uni` 的内置图标。如需自定义图标，请参考 `wot-design-uni` 的 `wd-icon` 组件文档进行扩展。
3.  **角标 (Badge)**：如果未来需要在 Tabbar 的 icon 上显示消息角标（如未读消息数），可以通过 Pinia 等状态管理库实现。在状态库中维护角标数量，然后在 `custom-tabbar` 组件中读取该状态，并传递给 `wd-tabbar-item` 的 `badge` 相关 props。
4.  **性能**：自定义 Tabbar 在性能上略逊于原生 Tabbar，尤其是在低端设备上。但在现代设备上，这种差异通常可以忽略不计。
5.  **路径一致性**：`pages.config.ts` 和 `tabbarList.ts` 中涉及的页面路径必须完全一致，且为绝对路径（以 `/` 开头）。
