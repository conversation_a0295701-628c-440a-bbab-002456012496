<view class="{{['data-v-1c35b924', 'commodity-selector-compact', s]}}"><view class="commodity-content data-v-1c35b924"><view wx:if="{{a}}" class="empty-commodity-compact data-v-1c35b924">{{b}}</view><view wx:else class="selected-commodity-compact data-v-1c35b924"><text class="commodity-name data-v-1c35b924">{{c}}</text></view></view><wd-button wx:if="{{d}}" class="data-v-1c35b924" u-s="{{['d']}}" bindclick="{{e}}" u-i="1c35b924-0" bind:__l="__l" u-p="{{f}}"> 选择 </wd-button><wd-button wx:else class="data-v-1c35b924" u-s="{{['d']}}" bindclick="{{g}}" u-i="1c35b924-1" bind:__l="__l" u-p="{{h||''}}"> 移除 </wd-button><wd-popup wx:if="{{r}}" class="data-v-1c35b924" u-s="{{['d']}}" u-i="1c35b924-2" bind:__l="__l" bindupdateModelValue="{{q}}" u-p="{{r}}"><view class="commodity-picker data-v-1c35b924"><view class="picker-header data-v-1c35b924"><text class="picker-title data-v-1c35b924">{{i}}</text></view><view class="picker-content data-v-1c35b924"><wd-search wx:if="{{l}}" class="data-v-1c35b924" bindsearch="{{j}}" u-i="1c35b924-3,1c35b924-2" bind:__l="__l" bindupdateModelValue="{{k}}" u-p="{{l}}"/><view class="commodity-list data-v-1c35b924"><view wx:if="{{m}}" class="loading-container data-v-1c35b924"><wd-loading wx:if="{{n}}" class="data-v-1c35b924" u-i="1c35b924-4,1c35b924-2" bind:__l="__l" u-p="{{n}}"/><text class="loading-text data-v-1c35b924">加载中...</text></view><view wx:elif="{{o}}" class="empty-container data-v-1c35b924"><text class="empty-text data-v-1c35b924">暂无商品数据</text></view><view wx:else class="data-v-1c35b924"><view wx:for="{{p}}" wx:for-item="commodity" wx:key="d" class="commodity-item data-v-1c35b924" bindtap="{{commodity.e}}"><view class="commodity-info data-v-1c35b924"><text class="commodity-name data-v-1c35b924">{{commodity.a}}</text></view><wd-radio wx:if="{{commodity.c}}" class="data-v-1c35b924" u-i="{{commodity.b}}" bind:__l="__l" u-p="{{commodity.c}}"/></view></view></view></view></view></wd-popup></view>