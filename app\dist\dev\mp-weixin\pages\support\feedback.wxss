/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.feedback-container.data-v-781c7fb7 {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 32rpx;
}
.feedback-form.data-v-781c7fb7 {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
.form-section.data-v-781c7fb7 {
  margin-bottom: 48rpx;
}
.form-section.data-v-781c7fb7:last-child {
  margin-bottom: 0;
}
.section-title.data-v-781c7fb7 {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.section-title .title-text.data-v-781c7fb7 {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}
.section-title .required.data-v-781c7fb7 {
  color: #f56c6c;
  margin-left: 8rpx;
}
.section-title .optional.data-v-781c7fb7 {
  color: #909399;
  font-size: 24rpx;
  margin-left: 8rpx;
}
.upload-section .image-list.data-v-781c7fb7 {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
  margin-bottom: 16rpx;
}
.upload-section .image-item.data-v-781c7fb7 {
  position: relative;
  width: 160rpx;
  height: 160rpx;
}
.upload-section .image-item .uploaded-image.data-v-781c7fb7 {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  border: 1rpx solid #e4e7ed;
}
.upload-section .image-item .delete-btn.data-v-781c7fb7 {
  position: absolute;
  top: -12rpx;
  right: -12rpx;
  width: 40rpx;
  height: 40rpx;
  background: #f56c6c;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}
.upload-section .upload-btn.data-v-781c7fb7 {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #d9d9d9;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
}
.upload-section .upload-btn .upload-text.data-v-781c7fb7 {
  font-size: 24rpx;
  color: #c0c4cc;
  margin-top: 8rpx;
}
.upload-section .upload-btn.data-v-781c7fb7:active {
  background: #f0f0f0;
}
.upload-section .upload-tip.data-v-781c7fb7 {
  font-size: 24rpx;
  color: #909399;
  line-height: 1.5;
}
.submit-section.data-v-781c7fb7 {
  margin-bottom: 32rpx;
}
.data-v-781c7fb7 .submit-btn {
  width: 100% !important;
  height: 88rpx !important;
  border-radius: 44rpx !important;
  font-size: 30rpx !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
}
.tips-section.data-v-781c7fb7 {
  background: #fff;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
.tips-title.data-v-781c7fb7 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.tips-title .tips-text.data-v-781c7fb7 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-left: 12rpx;
}
.tips-content .tip-item.data-v-781c7fb7 {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 12rpx;
}
.tips-content .tip-item.data-v-781c7fb7:last-child {
  margin-bottom: 0;
}