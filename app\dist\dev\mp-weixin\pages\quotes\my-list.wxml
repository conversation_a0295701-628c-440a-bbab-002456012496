<layout-default-uni class="data-v-f81ce579" u-s="{{['d']}}" u-i="f81ce579-0" bind:__l="__l"><view class="page-container data-v-f81ce579"><view class="header-section data-v-f81ce579"><view class="filter-tabs data-v-f81ce579"><view wx:for="{{a}}" wx:for-item="option" wx:key="b" class="{{['filter-tab', 'data-v-f81ce579', option.c && 'active']}}" bindtap="{{option.d}}">{{option.a}}</view></view></view><scroll-view class="scroll-container data-v-f81ce579" scroll-y refresher-enabled refresher-triggered="{{k}}" refresher-background="#f5f5f5" bindrefresherrefresh="{{l}}" bindrefresherrestore="{{m}}" bindscrolltolower="{{n}}"><view class="list-container data-v-f81ce579"><view wx:if="{{b}}" class="quotation-list data-v-f81ce579"><view wx:for="{{c}}" wx:for-item="quotation" wx:key="p" class="quotation-card data-v-f81ce579" bindtap="{{quotation.q}}"><view class="status-tag data-v-f81ce579" style="{{'color:' + quotation.b + ';' + ('background-color:' + quotation.c)}}">{{quotation.a}}</view><view class="card-header data-v-f81ce579"><text class="quotation-title data-v-f81ce579">{{quotation.d}}</text><text class="quotation-price data-v-f81ce579">{{quotation.e}}</text></view><view class="card-content data-v-f81ce579"><view class="info-row data-v-f81ce579"><text class="label data-v-f81ce579">商品：</text><text class="value data-v-f81ce579">{{quotation.f}}</text></view><view class="info-row data-v-f81ce579"><text class="label data-v-f81ce579">地点：</text><text class="value data-v-f81ce579">{{quotation.g}}</text></view><view wx:if="{{quotation.h}}" class="info-row data-v-f81ce579"><text class="label data-v-f81ce579">品牌：</text><text class="value data-v-f81ce579">{{quotation.i}}</text></view></view><view class="card-footer data-v-f81ce579"><text class="create-time data-v-f81ce579">{{quotation.j}}</text><text wx:if="{{quotation.k}}" class="{{['remaining-time', 'data-v-f81ce579', quotation.m && 'expired']}}">{{quotation.l}}</text></view><view class="action-buttons data-v-f81ce579" catchtap="{{quotation.o}}"><wd-button wx:for="{{quotation.n}}" wx:for-item="button" wx:key="b" class="data-v-f81ce579" u-s="{{['d']}}" bindclick="{{button.c}}" u-i="{{button.d}}" bind:__l="__l" u-p="{{button.e}}">{{button.a}}</wd-button></view></view></view><view wx:elif="{{d}}" class="empty-state data-v-f81ce579"><wd-img wx:if="{{e}}" class="data-v-f81ce579" u-i="f81ce579-2,f81ce579-0" bind:__l="__l" u-p="{{e}}"/><text class="empty-text data-v-f81ce579">暂无报价</text><wd-button wx:if="{{g}}" class="data-v-f81ce579" u-s="{{['d']}}" bindclick="{{f}}" u-i="f81ce579-3,f81ce579-0" bind:__l="__l" u-p="{{g}}"> 创建第一个报价 </wd-button></view><view wx:if="{{h}}" class="loading-more data-v-f81ce579"><wd-loading wx:if="{{i}}" class="data-v-f81ce579" u-i="f81ce579-4,f81ce579-0" bind:__l="__l" u-p="{{i}}"/><text class="loading-text data-v-f81ce579">加载中...</text></view><view wx:if="{{j}}" class="no-more data-v-f81ce579"><text class="data-v-f81ce579">没有更多数据了</text></view></view></scroll-view><view class="fab-container data-v-f81ce579"><wd-button wx:if="{{p}}" class="data-v-f81ce579" u-s="{{['d']}}" bindclick="{{o}}" u-i="f81ce579-5,f81ce579-0" bind:__l="__l" u-p="{{p}}"><text class="fab-icon data-v-f81ce579">+</text></wd-button></view></view></layout-default-uni>