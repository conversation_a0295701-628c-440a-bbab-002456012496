"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../common/vendor.js");
const api_commodity = require("../api/commodity.js");
if (!Array) {
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_wd_search2 = common_vendor.resolveComponent("wd-search");
  const _easycom_wd_loading2 = common_vendor.resolveComponent("wd-loading");
  const _easycom_wd_radio2 = common_vendor.resolveComponent("wd-radio");
  const _easycom_wd_popup2 = common_vendor.resolveComponent("wd-popup");
  (_easycom_wd_button2 + _easycom_wd_search2 + _easycom_wd_loading2 + _easycom_wd_radio2 + _easycom_wd_popup2)();
}
const _easycom_wd_button = () => "../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_wd_search = () => "../node-modules/wot-design-uni/components/wd-search/wd-search.js";
const _easycom_wd_loading = () => "../node-modules/wot-design-uni/components/wd-loading/wd-loading.js";
const _easycom_wd_radio = () => "../node-modules/wot-design-uni/components/wd-radio/wd-radio.js";
const _easycom_wd_popup = () => "../node-modules/wot-design-uni/components/wd-popup/wd-popup.js";
if (!Math) {
  (_easycom_wd_button + _easycom_wd_search + _easycom_wd_loading + _easycom_wd_radio + _easycom_wd_popup)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "CommoditySelector",
  props: {
    modelValue: { default: 0 },
    label: { default: "商品选择" },
    placeholder: { default: "请选择商品种类" },
    customClass: { default: "" },
    customLabelClass: { default: "" },
    customValueClass: { default: "" }
  },
  emits: ["update:modelValue", "change"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const showModal = common_vendor.ref(false);
    const searchKeyword = common_vendor.ref("");
    const selectedCommodity = common_vendor.ref(null);
    const availableCommodities = common_vendor.ref([]);
    const isLoading = common_vendor.ref(false);
    const currentCommodityId = common_vendor.computed({
      get: () => props.modelValue,
      set: (value) => {
        emit("update:modelValue", value);
      }
    });
    function loadAvailableCommodities() {
      return __async(this, null, function* () {
        try {
          isLoading.value = true;
          const res = yield api_commodity.getAllCommodityList();
          const allCommodities = res.data || [];
          if (searchKeyword.value.trim()) {
            availableCommodities.value = allCommodities.filter(
              (commodity) => commodity.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
            );
          } else {
            availableCommodities.value = allCommodities;
          }
        } catch (error) {
          console.error("获取商品列表失败:", error);
          common_vendor.index.showToast({
            title: "获取商品列表失败",
            icon: "error"
          });
          availableCommodities.value = [];
        } finally {
          isLoading.value = false;
        }
      });
    }
    function showCommodityPicker() {
      loadAvailableCommodities();
      showModal.value = true;
    }
    function searchCommodities() {
      loadAvailableCommodities();
    }
    function confirmCommoditySelection(commodity) {
      selectedCommodity.value = commodity;
      currentCommodityId.value = commodity.id;
      showModal.value = false;
      emit("change", commodity);
    }
    function removeCommodity() {
      selectedCommodity.value = null;
      currentCommodityId.value = 0;
      emit("change", null);
    }
    function initSelectedCommodity() {
      return __async(this, null, function* () {
        if (props.modelValue && props.modelValue > 0) {
          try {
            let commodity = availableCommodities.value.find((c) => c.id === props.modelValue);
            if (!commodity) {
              yield loadAvailableCommodities();
              commodity = availableCommodities.value.find((c) => c.id === props.modelValue);
            }
            selectedCommodity.value = commodity || null;
          } catch (error) {
            console.error("获取商品信息失败:", error);
            selectedCommodity.value = null;
          }
        }
      });
    }
    common_vendor.watch(() => props.modelValue, (newValue) => __async(this, null, function* () {
      if (newValue && newValue > 0 && (!selectedCommodity.value || selectedCommodity.value.id !== newValue)) {
        yield initSelectedCommodity();
      } else if (!newValue || newValue === 0) {
        selectedCommodity.value = null;
      }
    }), { immediate: true });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: !selectedCommodity.value
      }, !selectedCommodity.value ? {
        b: common_vendor.t(_ctx.placeholder)
      } : {
        c: common_vendor.t(selectedCommodity.value.name)
      }, {
        d: !selectedCommodity.value
      }, !selectedCommodity.value ? {
        e: common_vendor.o(showCommodityPicker),
        f: common_vendor.p({
          type: "primary",
          size: "small",
          ["custom-class"]: "dj-btn-primary"
        })
      } : {
        g: common_vendor.o(removeCommodity),
        h: common_vendor.p({
          type: "error",
          size: "small",
          ["custom-class"]: "dj-btn-danger"
        })
      }, {
        i: common_vendor.t(_ctx.label),
        j: common_vendor.o(searchCommodities),
        k: common_vendor.o(($event) => searchKeyword.value = $event),
        l: common_vendor.p({
          placeholder: "搜索商品种类",
          ["custom-class"]: "dj-search",
          modelValue: searchKeyword.value
        }),
        m: isLoading.value
      }, isLoading.value ? {
        n: common_vendor.p({
          type: "ring",
          color: "#667eea"
        })
      } : availableCommodities.value.length === 0 ? {} : {
        p: common_vendor.f(availableCommodities.value, (commodity, k0, i0) => {
          var _a;
          return {
            a: common_vendor.t(commodity.name),
            b: "1c35b924-5-" + i0 + ",1c35b924-2",
            c: common_vendor.p({
              value: ((_a = selectedCommodity.value) == null ? void 0 : _a.id) === commodity.id,
              ["custom-class"]: "dj-radio"
            }),
            d: commodity.id,
            e: common_vendor.o(($event) => confirmCommoditySelection(commodity), commodity.id)
          };
        })
      }, {
        o: availableCommodities.value.length === 0,
        q: common_vendor.o(($event) => showModal.value = $event),
        r: common_vendor.p({
          position: "bottom",
          ["custom-style"]: "height: 70%",
          ["custom-class"]: "dj-popup",
          modelValue: showModal.value
        }),
        s: common_vendor.n(_ctx.customClass)
      });
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-1c35b924"]]);
wx.createComponent(Component);
