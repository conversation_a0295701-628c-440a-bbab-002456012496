/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.contract-detail-page.data-v-790f05c0 {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}
.page-header.data-v-790f05c0 {
  position: sticky;
  top: 80rpx;
  z-index: 100;
  background: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
}
.page-header .page-title.data-v-790f05c0 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.page-header .header-actions.data-v-790f05c0 {
  display: flex;
  gap: 20rpx;
  align-items: center;
}
.loading.data-v-790f05c0 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx;
  color: #999;
}
.loading text.data-v-790f05c0 {
  margin-top: 20rpx;
  font-size: 28rpx;
}
.contract-detail.data-v-790f05c0 {
  padding: 0 20rpx 20rpx 20rpx;
}
.contract-detail .detail-section.data-v-790f05c0 {
  margin-bottom: 30rpx;
}
.contract-detail .detail-section .section-title.data-v-790f05c0 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.contract-detail .detail-section .info-card.data-v-790f05c0 {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.contract-detail .detail-section .info-card .info-row.data-v-790f05c0 {
  display: flex;
  margin-bottom: 20rpx;
}
.contract-detail .detail-section .info-card .info-row.data-v-790f05c0:last-child {
  margin-bottom: 0;
}
.contract-detail .detail-section .info-card .info-row .label.data-v-790f05c0 {
  flex-shrink: 0;
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}
.contract-detail .detail-section .info-card .info-row .value.data-v-790f05c0 {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.contract-detail .detail-section .info-card .info-row .value.generated-tag.data-v-790f05c0 {
  color: #ff9500;
  font-weight: bold;
}
.contract-detail .detail-section .info-card .info-row .value.pending-notice.data-v-790f05c0 {
  color: #ff6b35;
  font-style: italic;
}
.contract-detail .detail-section .info-card .info-row .value.frozen-text.data-v-790f05c0 {
  color: #f56c6c;
  font-weight: bold;
}
.contract-detail .detail-section .info-card .trade-rules-table.data-v-790f05c0 {
  width: 100%;
  font-size: 28rpx;
  color: #333;
  border: 1rpx solid #ebeef5;
  border-radius: 8rpx;
  overflow: hidden;
}
.contract-detail .detail-section .info-card .trade-rules-table .table-header.data-v-790f05c0 {
  display: flex;
  background-color: #f5f7fa;
  font-weight: bold;
}
.contract-detail .detail-section .info-card .trade-rules-table .table-header .table-cell.data-v-790f05c0 {
  flex: 1;
  padding: 16rpx 10rpx;
  text-align: center;
  border-right: 1rpx solid #ebeef5;
}
.contract-detail .detail-section .info-card .trade-rules-table .table-header .table-cell.data-v-790f05c0:last-child {
  border-right: none;
}
.contract-detail .detail-section .info-card .trade-rules-table .table-header .property-cell.data-v-790f05c0 {
  width: 180rpx;
  flex: none;
  background-color: #e6f3ff;
}
.contract-detail .detail-section .info-card .trade-rules-table .table-row.data-v-790f05c0 {
  display: flex;
  border-top: 1rpx solid #ebeef5;
}
.contract-detail .detail-section .info-card .trade-rules-table .table-row.data-v-790f05c0:nth-child(even) {
  background-color: #fafafa;
}
.contract-detail .detail-section .info-card .trade-rules-table .table-row .table-cell.data-v-790f05c0 {
  flex: 1;
  padding: 16rpx 10rpx;
  text-align: center;
  border-right: 1rpx solid #ebeef5;
}
.contract-detail .detail-section .info-card .trade-rules-table .table-row .table-cell.data-v-790f05c0:last-child {
  border-right: none;
}
.contract-detail .detail-section .info-card .trade-rules-table .table-row .property-cell.data-v-790f05c0 {
  width: 180rpx;
  flex: none;
  background-color: #f5f7fa;
  font-weight: 500;
  text-align: left;
  padding-left: 20rpx;
}
.contract-detail .detail-section .info-card .empty-table.data-v-790f05c0 {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
  border: 1rpx dashed #ebeef5;
  border-radius: 8rpx;
}
.contract-detail .detail-section .info-card .user-info .user-basic.data-v-790f05c0 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.contract-detail .detail-section .info-card .user-info .user-basic .user-name.data-v-790f05c0 {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.contract-detail .detail-section .info-card .user-info .user-basic .user-phone.data-v-790f05c0 {
  font-size: 24rpx;
  color: #999;
}
.contract-detail .detail-section .info-card .empty-user.data-v-790f05c0,
.contract-detail .detail-section .info-card .empty-executions.data-v-790f05c0 {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}
.contract-detail .detail-section .info-card .execution-list .execution-item.data-v-790f05c0 {
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}
.contract-detail .detail-section .info-card .execution-list .execution-item.data-v-790f05c0:last-child {
  border-bottom: none;
  margin-bottom: 0;
}
.contract-detail .detail-section .info-card .execution-list .execution-item .execution-header.data-v-790f05c0 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.contract-detail .detail-section .info-card .execution-list .execution-item .execution-header .execution-id.data-v-790f05c0 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}
.contract-detail .detail-section .info-card .execution-list .execution-item .execution-details.data-v-790f05c0 {
  padding-left: 20rpx;
}
.error-state.data-v-790f05c0 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx;
  color: #999;
  font-size: 28rpx;
  gap: 40rpx;
}