/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.trade-operation-form .horizontal-input-row.data-v-f4a108d6 {
  display: flex;
  align-items: center;
  gap: 12rpx;
  height: 80rpx;
}
.trade-operation-form .horizontal-input-row .input-label.data-v-f4a108d6 {
  width: 140rpx;
  text-align: right;
  padding-right: 8rpx;
}
.trade-operation-form .horizontal-input-row .input-label .label-text.data-v-f4a108d6 {
  font-size: 28rpx;
  color: #303133;
  font-weight: 500;
  white-space: nowrap;
}
.trade-operation-form .horizontal-input-row .min-value.data-v-f4a108d6 {
  width: 90rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.trade-operation-form .horizontal-input-row .min-value .range-text.data-v-f4a108d6 {
  font-size: 20rpx;
  color: #909399;
  line-height: 1;
  margin-bottom: 4rpx;
}
.trade-operation-form .horizontal-input-row .min-value .range-number.data-v-f4a108d6 {
  font-size: 22rpx;
  color: #606266;
  font-weight: 500;
  line-height: 1;
}
.trade-operation-form .horizontal-input-row .max-value.data-v-f4a108d6 {
  width: 90rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.trade-operation-form .horizontal-input-row .max-value .range-text.data-v-f4a108d6 {
  font-size: 20rpx;
  color: #909399;
  line-height: 1;
  margin-bottom: 4rpx;
}
.trade-operation-form .horizontal-input-row .max-value .range-number.data-v-f4a108d6 {
  font-size: 22rpx;
  color: #606266;
  font-weight: 500;
  line-height: 1;
}
.trade-operation-form .horizontal-input-row .input-with-controls.data-v-f4a108d6 {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border: 1rpx solid #dcdfe6;
  border-radius: 8rpx;
  overflow: hidden;
}
.trade-operation-form .horizontal-input-row .input-with-controls .control-button.data-v-f4a108d6 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f2f5;
  border-right: 1rpx solid #dcdfe6;
  cursor: pointer;
  transition: background-color 0.3s;
}
.trade-operation-form .horizontal-input-row .input-with-controls .control-button.data-v-f4a108d6:last-child {
  border-right: none;
  border-left: 1rpx solid #dcdfe6;
}
.trade-operation-form .horizontal-input-row .input-with-controls .control-button.data-v-f4a108d6:active {
  background-color: #e1e4e8;
}
.trade-operation-form .horizontal-input-row .input-with-controls .control-button .control-icon.data-v-f4a108d6 {
  font-size: 32rpx;
  color: #606266;
  font-weight: bold;
  line-height: 1;
}
.trade-operation-form .horizontal-input-row .input-with-controls .number-input.data-v-f4a108d6 {
  flex: 1;
  height: 60rpx;
  border: none;
  outline: none;
  background: transparent;
  text-align: center;
  font-size: 28rpx;
  color: #303133;
}
.trade-operation-form .horizontal-input-row .input-with-controls .number-input.data-v-f4a108d6::-moz-placeholder {
  color: #c0c4cc;
  font-size: 26rpx;
}
.trade-operation-form .horizontal-input-row .input-with-controls .number-input.data-v-f4a108d6::placeholder {
  color: #c0c4cc;
  font-size: 26rpx;
}
.trade-operation-form .horizontal-input-row .input-with-controls .number-input.readonly.data-v-f4a108d6 {
  color: #909399;
  background-color: #f5f7fa;
}
.trade-operation-form .error-tips.data-v-f4a108d6 {
  padding-left: 160rpx;
}
.trade-operation-form .error-tips .text-red-500.data-v-f4a108d6 {
  color: #f56565;
}
.trade-operation-form .estimated-price.data-v-f4a108d6 {
  padding: 12rpx 16rpx;
  background-color: #f7fafc;
  border-radius: 8rpx;
  text-align: center;
}