<view class="quotation-card data-v-c7b08e0d" bindtap="{{A}}"><view class="card-type-tag data-v-c7b08e0d"><wd-tag wx:if="{{b}}" class="data-v-c7b08e0d" u-s="{{['d']}}" u-i="c7b08e0d-0" bind:__l="__l" u-p="{{b}}">{{a}}</wd-tag></view><view class="card-body data-v-c7b08e0d"><view class="left-content data-v-c7b08e0d"><text class="quotation-title data-v-c7b08e0d">{{c}}</text><view class="publisher-info data-v-c7b08e0d"><text class="publisher-name data-v-c7b08e0d">{{d}}</text><text class="{{['remaining-time', 'data-v-c7b08e0d', f && 'expired']}}">{{e}}</text></view><view class="tag-info data-v-c7b08e0d"><wd-tag wx:if="{{g}}" class="data-v-c7b08e0d" u-s="{{['d']}}" u-i="c7b08e0d-1" bind:__l="__l" u-p="{{i}}">{{h}}</wd-tag><wd-tag wx:if="{{j}}" class="data-v-c7b08e0d" u-s="{{['d']}}" u-i="c7b08e0d-2" bind:__l="__l" u-p="{{l}}">{{k}}</wd-tag><wd-tag wx:if="{{m}}" class="data-v-c7b08e0d" u-s="{{['d']}}" u-i="c7b08e0d-3" bind:__l="__l" u-p="{{o}}">{{n}}</wd-tag></view></view><view class="right-content data-v-c7b08e0d"><view class="price-display data-v-c7b08e0d"><block wx:if="{{p}}"><text class="price-value adaptive-price data-v-c7b08e0d" style="{{'font-size:' + r}}" title="{{s}}">{{q}}</text></block><block wx:else><view class="basis-display data-v-c7b08e0d"><text class="contract-name data-v-c7b08e0d">{{t}}</text><text class="basis-value adaptive-price data-v-c7b08e0d" style="{{'font-size:' + w}}" data-positive="{{x}}" data-negative="{{y}}" title="{{z}}">{{v}}</text></view></block></view></view></view></view>