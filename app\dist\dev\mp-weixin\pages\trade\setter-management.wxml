<layout-default-uni class="data-v-fe014edc" u-s="{{['d']}}" u-i="fe014edc-0" bind:__l="__l"><view class="setter-management-page p-3 min-h-screen bg-gray-100 data-v-fe014edc"><view class="filter-bar bg-white rounded-lg shadow-sm p-3 mb-3 data-v-fe014edc"><wd-select-picker wx:if="{{d}}" class="data-v-fe014edc" u-s="{{['d']}}" bindchange="{{b}}" u-i="fe014edc-1,fe014edc-0" bind:__l="__l" bindupdateModelValue="{{c}}" u-p="{{d}}"><view class="status-display data-v-fe014edc"><text class="text-sm text-gray-600 mr-2 data-v-fe014edc">状态:</text><text class="status-text data-v-fe014edc">{{a}}</text></view></wd-select-picker></view><trade-request-list wx:if="{{j}}" class="data-v-fe014edc" bindfill="{{e}}" bindreject="{{f}}" bindconvertToSimulation="{{g}}" bindconvertToTrade="{{h}}" bindrefresh="{{i}}" u-i="fe014edc-2,fe014edc-0" bind:__l="__l" u-p="{{j}}"/><view wx:if="{{k}}" class="load-more-tip text-center py-4 data-v-fe014edc"><view wx:if="{{l}}" class="flex justify-center items-center data-v-fe014edc"><view class="loading-spinner w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2 data-v-fe014edc"></view><text class="text-sm text-gray-500 data-v-fe014edc">{{m}}</text></view><text wx:else class="text-xs text-gray-400 data-v-fe014edc">{{n}}</text></view><view wx:if="{{o}}" class="empty-state bg-white rounded-lg shadow-sm p-8 text-center data-v-fe014edc"><view class="empty-icon w-16 h-16 mx-auto mb-4 opacity-20 data-v-fe014edc"><svg wx:if="{{q}}" u-s="{{['d']}}" class="w-full h-full text-gray-400 data-v-fe014edc" u-i="fe014edc-3,fe014edc-0" bind:__l="__l" u-p="{{q}}"><path wx:if="{{p}}" class="data-v-fe014edc" u-i="fe014edc-4,fe014edc-3" bind:__l="__l" u-p="{{p}}"/></svg></view><text class="text-sm text-gray-400 block mb-4 data-v-fe014edc">暂无交易记录</text><wd-button wx:if="{{s}}" class="data-v-fe014edc" u-s="{{['d']}}" bindclick="{{r}}" u-i="fe014edc-5,fe014edc-0" bind:__l="__l" u-p="{{s}}"> 点击刷新 </wd-button></view><wd-popup wx:if="{{P}}" class="data-v-fe014edc" u-s="{{['d']}}" u-i="fe014edc-6,fe014edc-0" bind:__l="__l" bindupdateModelValue="{{O}}" u-p="{{P}}"><view class="feedback-dialog data-v-fe014edc"><view class="dialog-header data-v-fe014edc"><text class="dialog-title data-v-fe014edc">{{t}}</text></view><view class="dialog-content data-v-fe014edc"><view wx:if="{{v}}" class="request-summary data-v-fe014edc"><text class="summary-text data-v-fe014edc">{{w}} {{x}} 手 <text wx:if="{{y}}" class="data-v-fe014edc">@ {{z}}</text></text></view><view wx:if="{{A}}" class="fill-form data-v-fe014edc"><wd-input wx:if="{{C}}" class="data-v-fe014edc" u-i="fe014edc-7,fe014edc-6" bind:__l="__l" bindupdateModelValue="{{B}}" u-p="{{C}}"/><wd-input wx:if="{{E}}" class="mt-3 data-v-fe014edc" u-i="fe014edc-8,fe014edc-6" bind:__l="__l" bindupdateModelValue="{{D}}" u-p="{{E}}"/><wd-textarea wx:if="{{G}}" class="mt-3 data-v-fe014edc" u-i="fe014edc-9,fe014edc-6" bind:__l="__l" bindupdateModelValue="{{F}}" u-p="{{G}}"/></view><view wx:else class="reject-form data-v-fe014edc"><wd-textarea wx:if="{{I}}" class="data-v-fe014edc" u-i="fe014edc-10,fe014edc-6" bind:__l="__l" bindupdateModelValue="{{H}}" u-p="{{I}}"/></view></view><view class="dialog-actions data-v-fe014edc"><wd-button wx:if="{{K}}" class="data-v-fe014edc" u-s="{{['d']}}" bindclick="{{J}}" u-i="fe014edc-11,fe014edc-6" bind:__l="__l" u-p="{{K}}"> 取消 </wd-button><wd-button wx:if="{{N}}" class="data-v-fe014edc" u-s="{{['d']}}" bindclick="{{M}}" u-i="fe014edc-12,fe014edc-6" bind:__l="__l" u-p="{{N}}"> 确认{{L}}</wd-button></view></view></wd-popup></view></layout-default-uni>