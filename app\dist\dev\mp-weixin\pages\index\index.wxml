<layout-tabbar-uni u-s="{{['d']}}" u-i="d646dfc4-0" bind:__l="__l"><view class="bg-white px-4 pt-2" style="{{'margin-top:' + d}}"><view class="mt-10"><image src="{{a}}" alt="" class="mx-auto block h-28 w-28"/></view><view class="mt-4 text-center text-4xl text-_a__a_d14328_a_"> unibest </view><view class="mb-8 mt-2 text-center text-2xl"> 最好用的 uniapp 开发模板 </view><view class="m-auto mb-2 max-w-100 text-justify indent text-4">{{b}}</view><view class="mt-4 text-center"> 作者： <text class="text-green-500"> 菲鸽 </text></view><view class="mt-4 text-center"> 官网地址： <text class="text-green-500"> https://unibest.tech </text></view><view class="mt-4 text-center"><wd-button wx:if="{{c}}" u-s="{{['d']}}" u-i="d646dfc4-1,d646dfc4-0" bind:__l="__l" u-p="{{c}}"> UI组件按钮 </wd-button></view><view class="mt-4 text-center"> UI组件官网：<text class="text-green-500"> https://wot-design-uni.cn </text></view></view></layout-tabbar-uni>