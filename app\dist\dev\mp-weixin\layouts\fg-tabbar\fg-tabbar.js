"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const layouts_fgTabbar_tabbar = require("./tabbar.js");
const layouts_fgTabbar_tabbarList = require("./tabbarList.js");
if (!Array) {
  const _easycom_wd_tabbar_item2 = common_vendor.resolveComponent("wd-tabbar-item");
  const _easycom_wd_tabbar2 = common_vendor.resolveComponent("wd-tabbar");
  (_easycom_wd_tabbar_item2 + _easycom_wd_tabbar2)();
}
const _easycom_wd_tabbar_item = () => "../../node-modules/wot-design-uni/components/wd-tabbar-item/wd-tabbar-item.js";
const _easycom_wd_tabbar = () => "../../node-modules/wot-design-uni/components/wd-tabbar/wd-tabbar.js";
if (!Math) {
  (_easycom_wd_tabbar_item + _easycom_wd_tabbar)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "fg-tabbar",
  setup(__props) {
    const customTabbarEnable = layouts_fgTabbar_tabbarList.selectedTabbarStrategy === layouts_fgTabbar_tabbarList.TABBAR_MAP.CUSTOM_TABBAR_WITHOUT_CACHE;
    const tabbarList = layouts_fgTabbar_tabbarList.tabbarList.map((item) => __spreadProps(__spreadValues({}, item), { path: `/${item.pagePath}` }));
    function selectTabBar({ value: index }) {
      const url = tabbarList[index].path;
      layouts_fgTabbar_tabbar.tabbarStore.setCurIdx(index);
      {
        common_vendor.index.switchTab({ url });
      }
    }
    common_vendor.onLoad(() => {
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.unref(customTabbarEnable)
      }, common_vendor.unref(customTabbarEnable) ? {
        b: common_vendor.f(common_vendor.unref(tabbarList), (item, idx, i0) => {
          return common_vendor.e({
            a: item.iconType === "uiLib"
          }, item.iconType === "uiLib" ? {
            b: "60c2f093-1-" + i0 + ",60c2f093-0",
            c: common_vendor.p({
              title: item.text,
              icon: item.icon
            })
          } : item.iconType === "unocss" || item.iconType === "iconfont" ? {
            e: common_vendor.n(item.icon),
            f: common_vendor.n(idx === common_vendor.unref(layouts_fgTabbar_tabbar.tabbarStore).curIdx ? "is-active" : "is-inactive"),
            g: "60c2f093-2-" + i0 + ",60c2f093-0",
            h: common_vendor.p({
              title: item.text
            })
          } : item.iconType === "local" ? {
            j: item.icon,
            k: "60c2f093-3-" + i0 + ",60c2f093-0",
            l: common_vendor.p({
              title: item.text
            })
          } : {}, {
            d: item.iconType === "unocss" || item.iconType === "iconfont",
            i: item.iconType === "local",
            m: item.path
          });
        }),
        c: common_vendor.o(selectTabBar),
        d: common_vendor.o(($event) => common_vendor.unref(layouts_fgTabbar_tabbar.tabbarStore).curIdx = $event),
        e: common_vendor.p({
          bordered: true,
          ["safe-area-inset-bottom"]: true,
          placeholder: true,
          fixed: true,
          modelValue: common_vendor.unref(layouts_fgTabbar_tabbar.tabbarStore).curIdx
        })
      } : {});
    };
  }
});
wx.createComponent(_sfc_main);
