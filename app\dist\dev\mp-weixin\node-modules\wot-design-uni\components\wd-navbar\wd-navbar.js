"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../../common/vendor.js");
if (!Math) {
  wdIcon();
}
const wdIcon = () => "../wd-icon/wd-icon.js";
const __default__ = {
  name: "wd-navbar",
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: common_vendor.navbarProps,
  emits: ["click-left", "click-right"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const height = common_vendor.ref("");
    const { statusBarHeight } = common_vendor.index.getSystemInfoSync();
    common_vendor.watch(
      [() => props.fixed, () => props.placeholder],
      () => {
        setPlaceholderHeight();
      },
      { deep: true, immediate: false }
    );
    const rootStyle = common_vendor.computed(() => {
      const style = {};
      if (props.fixed && common_vendor.isDef(props.zIndex)) {
        style["z-index"] = props.zIndex;
      }
      if (props.safeAreaInsetTop) {
        style["padding-top"] = common_vendor.addUnit(statusBarHeight || 0);
      }
      return `${common_vendor.objToStyle(style)}${props.customStyle}`;
    });
    common_vendor.onMounted(() => {
      if (props.fixed && props.placeholder) {
        common_vendor.nextTick$1(() => {
          setPlaceholderHeight();
        });
      }
    });
    function handleClickLeft() {
      if (!props.leftDisabled) {
        emit("click-left");
      }
    }
    function handleClickRight() {
      if (!props.rightDisabled) {
        emit("click-right");
      }
    }
    const { proxy } = common_vendor.getCurrentInstance();
    function setPlaceholderHeight() {
      if (!props.fixed || !props.placeholder) {
        return;
      }
      common_vendor.getRect(".wd-navbar", false, proxy).then((res) => {
        height.value = res.height;
      });
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: _ctx.$slots.capsule
      }, _ctx.$slots.capsule ? {} : !_ctx.$slots.left ? common_vendor.e({
        c: _ctx.leftArrow
      }, _ctx.leftArrow ? {
        d: common_vendor.p({
          name: "arrow-left",
          ["custom-class"]: "wd-navbar__arrow"
        })
      } : {}, {
        e: _ctx.leftText
      }, _ctx.leftText ? {
        f: common_vendor.t(_ctx.leftText)
      } : {}, {
        g: common_vendor.n(`wd-navbar__left ${_ctx.leftDisabled ? "is-disabled" : ""}`),
        h: common_vendor.o(handleClickLeft)
      }) : {
        i: common_vendor.n(`wd-navbar__left ${_ctx.leftDisabled ? "is-disabled" : ""}`),
        j: common_vendor.o(handleClickLeft)
      }, {
        b: !_ctx.$slots.left,
        k: !_ctx.$slots.title && _ctx.title
      }, !_ctx.$slots.title && _ctx.title ? {
        l: common_vendor.t(_ctx.title)
      } : {}, {
        m: _ctx.$slots.right || _ctx.rightText
      }, _ctx.$slots.right || _ctx.rightText ? common_vendor.e({
        n: !_ctx.$slots.right && _ctx.rightText
      }, !_ctx.$slots.right && _ctx.rightText ? {
        o: common_vendor.t(_ctx.rightText)
      } : {}, {
        p: common_vendor.n(`wd-navbar__right ${_ctx.rightDisabled ? "is-disabled" : ""}`),
        q: common_vendor.o(handleClickRight)
      }) : {}, {
        r: common_vendor.n(`wd-navbar ${_ctx.customClass} ${_ctx.fixed ? "is-fixed" : ""} ${_ctx.bordered ? "is-border" : ""}`),
        s: common_vendor.s(rootStyle.value),
        t: common_vendor.unref(common_vendor.addUnit)(height.value)
      });
    };
  }
}));
wx.createComponent(_sfc_main);
