<view class="{{['data-v-d36d9223', 'user-selector-compact', t]}}"><view class="user-content data-v-d36d9223"><view wx:if="{{a}}" class="empty-user-compact data-v-d36d9223">{{b}}</view><view wx:else class="selected-user-compact data-v-d36d9223"><text class="user-name data-v-d36d9223">{{c}}</text><text class="user-phone data-v-d36d9223">{{d}}</text></view></view><wd-button wx:if="{{e}}" class="data-v-d36d9223" u-s="{{['d']}}" bindclick="{{f}}" u-i="d36d9223-0" bind:__l="__l" u-p="{{g}}"> 选择 </wd-button><wd-button wx:else class="data-v-d36d9223" u-s="{{['d']}}" bindclick="{{h}}" u-i="d36d9223-1" bind:__l="__l" u-p="{{i||''}}"> 移除 </wd-button><wd-popup wx:if="{{s}}" class="data-v-d36d9223" u-s="{{['d']}}" u-i="d36d9223-2" bind:__l="__l" bindupdateModelValue="{{r}}" u-p="{{s}}"><view class="user-picker data-v-d36d9223"><view class="picker-header data-v-d36d9223"><text class="picker-title data-v-d36d9223">{{j}}</text></view><view class="picker-content data-v-d36d9223"><wd-search wx:if="{{m}}" class="data-v-d36d9223" bindsearch="{{k}}" u-i="d36d9223-3,d36d9223-2" bind:__l="__l" bindupdateModelValue="{{l}}" u-p="{{m}}"/><view class="user-list data-v-d36d9223"><view wx:if="{{n}}" class="loading-container data-v-d36d9223"><wd-loading wx:if="{{o}}" class="data-v-d36d9223" u-i="d36d9223-4,d36d9223-2" bind:__l="__l" u-p="{{o}}"/><text class="loading-text data-v-d36d9223">加载中...</text></view><view wx:elif="{{p}}" class="empty-container data-v-d36d9223"><text class="empty-text data-v-d36d9223">暂无用户数据</text></view><view wx:else class="data-v-d36d9223"><view wx:for="{{q}}" wx:for-item="user" wx:key="e" class="user-item data-v-d36d9223" bindtap="{{user.f}}"><view class="user-info data-v-d36d9223"><text class="user-name data-v-d36d9223">{{user.a}}</text><text class="user-phone data-v-d36d9223">{{user.b}}</text></view><wd-radio wx:if="{{user.d}}" class="data-v-d36d9223" u-i="{{user.c}}" bind:__l="__l" u-p="{{user.d}}"/></view></view></view></view></view></wd-popup></view>