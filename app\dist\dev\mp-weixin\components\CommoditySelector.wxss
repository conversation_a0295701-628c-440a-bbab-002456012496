/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.commodity-selector-compact.data-v-1c35b924 {
  display: flex;
  align-items: center;
  gap: 20rpx;
  background-color: #f9fafc;
  padding: 24rpx;
  border-radius: 8rpx;
  position: relative;
  overflow: hidden;
}
.commodity-selector-compact.data-v-1c35b924::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: linear-gradient(180deg, #764ba2, #667eea);
}
.commodity-selector-compact .commodity-content.data-v-1c35b924 {
  flex: 1;
}
.commodity-selector-compact .commodity-content .empty-commodity-compact.data-v-1c35b924 {
  color: #909399;
  font-size: 28rpx;
  font-style: italic;
}
.commodity-selector-compact .commodity-content .selected-commodity-compact.data-v-1c35b924 {
  display: flex;
  align-items: center;
  gap: 15rpx;
  background-color: rgba(102, 126, 234, 0.05);
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
}
.commodity-selector-compact .commodity-content .selected-commodity-compact .commodity-name.data-v-1c35b924 {
  font-size: 28rpx;
  color: #303133;
  font-weight: 500;
}
.commodity-selector-compact .commodity-content .selected-commodity-compact .commodity-symbol.data-v-1c35b924 {
  font-size: 24rpx;
  color: #606266;
  background-color: rgba(102, 126, 234, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}
.commodity-picker.data-v-1c35b924 {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.commodity-picker .picker-header.data-v-1c35b924 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.commodity-picker .picker-header .picker-title.data-v-1c35b924 {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.commodity-picker .picker-content.data-v-1c35b924 {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
  background-color: #f9fafc;
}
.commodity-picker .picker-content .commodity-list.data-v-1c35b924 {
  margin-top: 20rpx;
}
.commodity-picker .picker-content .commodity-list .loading-container.data-v-1c35b924 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}
.commodity-picker .picker-content .commodity-list .loading-container .loading-text.data-v-1c35b924 {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #606266;
}
.commodity-picker .picker-content .commodity-list .empty-container.data-v-1c35b924 {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
}
.commodity-picker .picker-content .commodity-list .empty-container .empty-text.data-v-1c35b924 {
  font-size: 28rpx;
  color: #909399;
}
.commodity-picker .picker-content .commodity-list .commodity-item.data-v-1c35b924 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx;
  background: white;
  border-radius: 8rpx;
  margin-bottom: 15rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}
.commodity-picker .picker-content .commodity-list .commodity-item.data-v-1c35b924::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: linear-gradient(180deg, #667eea, #764ba2);
}
.commodity-picker .picker-content .commodity-list .commodity-item.data-v-1c35b924:hover {
  background-color: #f9fafc;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.15);
}
.commodity-picker .picker-content .commodity-list .commodity-item .commodity-info.data-v-1c35b924 {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.commodity-picker .picker-content .commodity-list .commodity-item .commodity-info .commodity-name.data-v-1c35b924 {
  font-size: 32rpx;
  color: #303133;
  font-weight: 500;
}
.commodity-picker .picker-content .commodity-list .commodity-item .commodity-info .commodity-symbol.data-v-1c35b924 {
  font-size: 24rpx;
  color: #606266;
  background-color: rgba(102, 126, 234, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}
.data-v-1c35b924  .dj-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}
.data-v-1c35b924  .dj-btn-primary:hover {
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
  transform: translateY(-1rpx);
}
.data-v-1c35b924  .dj-btn-danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  border: none;
  color: white;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}
.data-v-1c35b924  .dj-btn-danger:hover {
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.4);
  transform: translateY(-1rpx);
}
.data-v-1c35b924  .dj-search {
  background: white;
  border-radius: 40rpx;
  padding: 0 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}
.data-v-1c35b924  .dj-search:focus-within {
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.15);
}
.data-v-1c35b924  .dj-radio .wd-radio__label {
  color: #303133;
  font-size: 28rpx;
  font-weight: 500;
  padding-left: 12rpx;
}
.data-v-1c35b924  .dj-radio .wd-radio__shape {
  border-color: #c0c4cc;
  transition: all 0.3s ease;
}
.data-v-1c35b924  .dj-radio .wd-radio__shape.is-checked {
  background-color: #667eea;
  border-color: #667eea;
  box-shadow: 0 0 4rpx rgba(102, 126, 234, 0.3);
}
.data-v-1c35b924  .dj-popup {
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
}