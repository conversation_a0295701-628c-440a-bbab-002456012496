<layout-default-uni class="data-v-456eb489" u-s="{{['d']}}" u-i="456eb489-0" bind:__l="__l"><view class="trade-execute-page min-h-screen bg-gray-100 data-v-456eb489"><wd-navbar wx:if="{{a}}" class="data-v-456eb489" u-i="456eb489-1,456eb489-0" bind:__l="__l" u-p="{{a}}"/><view class="p-3 data-v-456eb489"><view class="card bg-white rounded-lg shadow-sm mb-3 data-v-456eb489"><view class="tab-container data-v-456eb489"><view class="{{['tab-item', 'data-v-456eb489', b && 'active']}}" bindtap="{{c}}"><text class="tab-text data-v-456eb489">点价</text></view><view class="{{['tab-item', 'data-v-456eb489', d && 'active']}}" bindtap="{{e}}"><text class="tab-text data-v-456eb489">洗基差</text></view></view></view><view class="card bg-white rounded-lg shadow-sm p-4 mb-3 data-v-456eb489"><view class="mb-4 data-v-456eb489"><combination-selector wx:if="{{h}}" class="data-v-456eb489" bindchange="{{f}}" u-i="456eb489-2,456eb489-0" bind:__l="__l" bindupdateModelValue="{{g}}" u-p="{{h}}"/></view><view class="stats-grid grid grid-cols-4 gap-2 data-v-456eb489"><view class="stat-item bg-blue-50 rounded-lg p-3 text-center data-v-456eb489"><text class="stat-number text-lg font-bold text-blue-600 data-v-456eb489">{{i}}</text><text class="stat-label text-xs text-gray-500 block mt-1 data-v-456eb489">{{j}}剩余</text></view><view class="stat-item bg-orange-50 rounded-lg p-3 text-center data-v-456eb489"><text class="stat-number text-lg font-bold text-orange-600 data-v-456eb489">{{k}}</text><text class="stat-label text-xs text-gray-500 block mt-1 data-v-456eb489">{{l}}冻结</text></view><view class="stat-item bg-green-50 rounded-lg p-3 text-center data-v-456eb489"><text class="stat-number text-lg font-bold text-green-600 data-v-456eb489">{{m}}</text><text class="stat-label text-xs text-gray-500 block mt-1 data-v-456eb489">{{n}}可用</text></view><view class="stat-item bg-purple-50 rounded-lg p-3 text-center data-v-456eb489"><text class="stat-number text-lg font-bold text-purple-600 data-v-456eb489">{{o}}</text><text class="stat-label text-xs text-gray-500 block mt-1 data-v-456eb489">{{p}}均价</text></view></view></view><quote-card wx:if="{{r}}" class="data-v-456eb489" bindpriceClick="{{q}}" u-i="456eb489-3,456eb489-0" bind:__l="__l" u-p="{{r}}"/><view class="mb-3 data-v-456eb489"><trade-operation-form wx:if="{{v}}" class="r data-v-456eb489" u-r="tradeFormRef" bindsubmit="{{t}}" u-i="456eb489-4,456eb489-0" bind:__l="__l" u-p="{{v}}"/></view><trade-request-list wx:if="{{w}}" class="data-v-456eb489" u-i="456eb489-5,456eb489-0" bind:__l="__l" u-p="{{w}}"/></view></view></layout-default-uni>