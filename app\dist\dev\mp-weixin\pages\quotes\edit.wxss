/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.quotation-form-page.data-v-363e46db {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 120rpx;
}
.page-header.data-v-363e46db {
  padding: 30rpx 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}
.page-header .page-title.data-v-363e46db {
  font-size: 40rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.loading-container.data-v-363e46db {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
}
.loading-container .loading-text.data-v-363e46db {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #606266;
}
.form-container.data-v-363e46db {
  padding: 20rpx;
}
.data-v-363e46db .form-section {
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}
.cell-value.data-v-363e46db {
  color: #303133;
  font-size: 28rpx;
}
.price-unit.data-v-363e46db {
  color: #606266;
  font-size: 24rpx;
  margin-left: 10rpx;
}
.form-actions.data-v-363e46db {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 20rpx;
  padding: 20rpx;
  background: white;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.form-actions.data-v-363e46db .wd-button {
  flex: 1;
}
.data-v-363e46db  .dj-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}
.data-v-363e46db  .dj-btn-primary:hover {
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
  transform: translateY(-1rpx);
}
.data-v-363e46db  .dj-btn-secondary {
  background: #f8f9fa;
  border: 1rpx solid #e0e3ea;
  color: #303133;
  font-weight: 500;
  transition: all 0.3s ease;
}
.data-v-363e46db  .dj-btn-secondary:hover {
  background: #e9ecef;
  border-color: #d0d3d9;
}
.data-v-363e46db  .dj-price-type-radio-group {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 100%;
}
.data-v-363e46db  .dj-price-type-radio-group .wd-radio {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 0;
}