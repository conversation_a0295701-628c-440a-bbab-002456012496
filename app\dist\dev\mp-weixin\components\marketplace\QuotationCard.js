"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _easycom_wd_tag2 = common_vendor.resolveComponent("wd-tag");
  _easycom_wd_tag2();
}
const _easycom_wd_tag = () => "../../node-modules/wot-design-uni/components/wd-tag/wd-tag.js";
if (!Math) {
  _easycom_wd_tag();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "QuotationCard"
}), {
  __name: "QuotationCard",
  props: {
    quotation: {}
  },
  emits: ["click"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    function formatRemainingTime(quotation) {
      if (quotation.isExpired) {
        return "已过期";
      }
      if (quotation.remainingHours <= 0) {
        return "即将过期";
      } else if (quotation.remainingHours < 24) {
        return `剩余 ${quotation.remainingHours} 小时`;
      } else {
        const days = Math.floor(quotation.remainingHours / 24);
        return `剩余 ${days} 天`;
      }
    }
    function getCompanyShortName(fullName) {
      if (!fullName)
        return "未知企业";
      if (fullName.length > 8) {
        return fullName.substring(0, 8) + "...";
      }
      return fullName;
    }
    function formatLargeNumber(num) {
      const absNum = Math.abs(num);
      const sign = num < 0 ? "-" : "";
      if (absNum < 1e4) {
        return sign + absNum.toLocaleString();
      }
      if (absNum < 1e8) {
        const wan = absNum / 1e4;
        if (wan >= 100) {
          return sign + Math.round(wan) + "万";
        } else {
          return sign + wan.toFixed(1) + "万";
        }
      }
      const yi = absNum / 1e8;
      if (yi >= 100) {
        return sign + Math.round(yi) + "亿";
      } else {
        return sign + yi.toFixed(1) + "亿";
      }
    }
    function formatBasisPrice(price) {
      const formatted = formatLargeNumber(Math.abs(price));
      return price >= 0 ? `+${formatted}` : `-${formatted}`;
    }
    function calculateDynamicFontSize(text, baseSize = 48, minSize = 28) {
      const textLength = text.length;
      if (textLength <= 6)
        return baseSize;
      if (textLength <= 8)
        return Math.max(baseSize * 0.9, minSize);
      if (textLength <= 10)
        return Math.max(baseSize * 0.8, minSize);
      return minSize;
    }
    function getContractName(quotation) {
      var _a;
      if ((_a = quotation.instrumentRef) == null ? void 0 : _a.instrument_id) {
        return quotation.instrumentRef.instrument_id;
      }
      return "未指定合约";
    }
    function handleCardClick() {
      emit("click", props.quotation);
    }
    return (_ctx, _cache) => {
      var _a, _b, _c;
      return common_vendor.e({
        a: common_vendor.t(_ctx.quotation.priceType === "Fixed" ? "一口价" : "基差"),
        b: common_vendor.p({
          type: _ctx.quotation.priceType === "Fixed" ? "primary" : "danger",
          size: "small"
        }),
        c: common_vendor.t(_ctx.quotation.title),
        d: common_vendor.t(getCompanyShortName(((_a = _ctx.quotation.user) == null ? void 0 : _a.nickName) || "")),
        e: common_vendor.t(formatRemainingTime(_ctx.quotation)),
        f: _ctx.quotation.isExpired ? 1 : "",
        g: (_b = _ctx.quotation.commodity) == null ? void 0 : _b.name
      }, ((_c = _ctx.quotation.commodity) == null ? void 0 : _c.name) ? {
        h: common_vendor.t(_ctx.quotation.commodity.name),
        i: common_vendor.p({
          type: "primary",
          size: "small"
        })
      } : {}, {
        j: _ctx.quotation.deliveryLocation
      }, _ctx.quotation.deliveryLocation ? {
        k: common_vendor.t(_ctx.quotation.deliveryLocation),
        l: common_vendor.p({
          type: "success",
          size: "small"
        })
      } : {}, {
        m: _ctx.quotation.brand
      }, _ctx.quotation.brand ? {
        n: common_vendor.t(_ctx.quotation.brand),
        o: common_vendor.p({
          type: "warning",
          size: "small"
        })
      } : {}, {
        p: _ctx.quotation.priceType === "Fixed"
      }, _ctx.quotation.priceType === "Fixed" ? {
        q: common_vendor.t(formatLargeNumber(_ctx.quotation.price)),
        r: calculateDynamicFontSize(formatLargeNumber(_ctx.quotation.price)) + "rpx",
        s: _ctx.quotation.price.toLocaleString()
      } : {
        t: common_vendor.t(getContractName(_ctx.quotation)),
        v: common_vendor.t(formatBasisPrice(_ctx.quotation.price)),
        w: calculateDynamicFontSize(formatBasisPrice(_ctx.quotation.price), 42, 26) + "rpx",
        x: _ctx.quotation.price >= 0,
        y: _ctx.quotation.price < 0,
        z: (_ctx.quotation.price >= 0 ? "+" : "") + _ctx.quotation.price.toLocaleString()
      }, {
        A: common_vendor.o(handleCardClick)
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-c7b08e0d"]]);
wx.createComponent(Component);
