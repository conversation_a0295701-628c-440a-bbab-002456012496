/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.setter-management-page.data-v-fe014edc {
  background-color: #f7f8fa;
}
.load-more-tip .loading-spinner.data-v-fe014edc {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #409eff;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin-fe014edc 1s linear infinite;
}
@keyframes spin-fe014edc {
to {
    transform: rotate(360deg);
}
}
.empty-state .empty-icon.data-v-fe014edc {
  width: 128rpx;
  height: 128rpx;
  opacity: 0.3;
}
.feedback-dialog.data-v-fe014edc {
  width: 600rpx;
  background: white;
  border-radius: 12rpx;
  overflow: hidden;
}
.feedback-dialog .dialog-header.data-v-fe014edc {
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.feedback-dialog .dialog-header .dialog-title.data-v-fe014edc {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.feedback-dialog .dialog-content.data-v-fe014edc {
  padding: 30rpx;
}
.feedback-dialog .dialog-content .request-summary.data-v-fe014edc {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}
.feedback-dialog .dialog-content .request-summary .summary-text.data-v-fe014edc {
  font-size: 28rpx;
  color: #333;
}
.feedback-dialog .dialog-actions.data-v-fe014edc {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 30rpx 40rpx;
  justify-content: flex-end;
}
.filter-bar .status-display.data-v-fe014edc {
  padding: 8rpx 16rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e9ecef;
}
.filter-bar .status-display .status-text.data-v-fe014edc {
  color: #495057;
  font-size: 28rpx;
}