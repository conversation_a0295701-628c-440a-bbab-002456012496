"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const api_traderequest = require("../../api/traderequest.js");
const utils_toast = require("../../utils/toast.js");
const composables_useContractData = require("../../composables/useContractData.js");
const store_market = require("../../store/market.js");
if (!Array) {
  const _easycom_wd_navbar2 = common_vendor.resolveComponent("wd-navbar");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_navbar2 + _component_layout_default_uni)();
}
const _easycom_wd_navbar = () => "../../node-modules/wot-design-uni/components/wd-navbar/wd-navbar.js";
if (!Math) {
  (_easycom_wd_navbar + CombinationSelector + QuoteCard + TradeOperationForm + TradeRequestList)();
}
const QuoteCard = () => "../../components/QuoteCard.js";
const CombinationSelector = () => "./CombinationSelector2.js";
const TradeOperationForm = () => "./TradeOperationForm2.js";
const TradeRequestList = () => "../../components/TradeRequestList.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "TradeExecutePage"
}), {
  __name: "execute",
  setup(__props) {
    const {
      instrumentId,
      setterID,
      setterName,
      instrument,
      availableCombinations,
      currentCombinationDisplay,
      pickerValue,
      getContractStatsByType,
      loadInstrument,
      loadAllAvailableContracts,
      handleCombinationChange,
      resetContractData
    } = composables_useContractData.useContractData();
    const requestType = common_vendor.ref("PointPrice");
    const todayTradeRequests = common_vendor.ref([]);
    const loading = common_vendor.ref(false);
    const marketStore = store_market.useMarketStore();
    const isPointPrice = common_vendor.computed(() => requestType.value === "PointPrice");
    const currentContractStats = common_vendor.computed(() => getContractStatsByType(isPointPrice.value).value);
    const marketData = common_vendor.computed(() => {
      var _a;
      const symbol = (_a = instrument.value) == null ? void 0 : _a.instrument_id;
      if (!symbol)
        return null;
      return marketStore.getMarketData(symbol);
    });
    const limitUpPrice = common_vendor.computed(() => {
      return marketData.value ? parseFloat(marketData.value.limit_up) : void 0;
    });
    const limitDownPrice = common_vendor.computed(() => {
      return marketData.value ? parseFloat(marketData.value.limit_down) : void 0;
    });
    const tradeFormRef = common_vendor.ref();
    const handlePriceClick = (event) => {
      if (tradeFormRef.value) {
        tradeFormRef.value.handlePriceClick(event.numericPrice);
        utils_toast.toast.success(`已选择${getPriceTypeName(event.type)}价格: ${event.price}`);
      }
    };
    const getPriceTypeName = (type) => {
      const typeNames = {
        last: "最新",
        bid: "买一",
        ask: "卖一",
        limit_up: "涨停",
        limit_down: "跌停"
      };
      return typeNames[type];
    };
    function loadTodayTradeRequests() {
      return __async(this, null, function* () {
        try {
          const today = /* @__PURE__ */ new Date();
          const formatDate = (date) => {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, "0");
            const day = String(date.getDate()).padStart(2, "0");
            const hours = String(date.getHours()).padStart(2, "0");
            const minutes = String(date.getMinutes()).padStart(2, "0");
            const seconds = String(date.getSeconds()).padStart(2, "0");
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
          };
          const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0);
          const todayEnd = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
          const params = {
            instrumentRefID: instrumentId.value || void 0,
            startDate: formatDate(todayStart),
            // 今天 00:00:00
            endDate: formatDate(todayEnd)
            // 今天 23:59:59
          };
          console.log("开始加载今日交易请求", params);
          console.log("日期范围:", {
            startDate: params.startDate,
            endDate: params.endDate,
            todayStart: todayStart.toISOString(),
            todayEnd: todayEnd.toISOString()
          });
          const response = yield api_traderequest.getMyTradeRequestsAsPricer(params);
          console.log("今日交易请求API响应:", response);
          if (response.code === 0) {
            const filteredRequests = response.data.list.filter(
              (request) => !setterID.value || request.setterID === setterID.value
            );
            todayTradeRequests.value = filteredRequests;
            console.log("今日交易请求加载成功:", filteredRequests);
          } else {
            console.warn("今日交易请求API返回错误:", response.msg);
          }
        } catch (error) {
          console.error("加载今日交易请求失败:", error);
        }
      });
    }
    function handleTradeRequestSubmit(data) {
      return __async(this, null, function* () {
        if (!instrumentId.value) {
          utils_toast.toast.error("期货合约信息缺失");
          return;
        }
        if (!setterID.value) {
          utils_toast.toast.error("被点价方信息缺失");
          return;
        }
        loading.value = true;
        try {
          let response;
          const { quantity, price } = data;
          console.log("发送交易请求参数:", {
            setterID: setterID.value,
            instrumentId: instrumentId.value,
            quantity,
            price,
            requestType: requestType.value
          });
          if (isPointPrice.value) {
            response = yield api_traderequest.pointPrice(
              setterID.value,
              instrumentId.value,
              quantity,
              price,
              "MANUAL",
              // V4: 增加执行模式
              new Date(Date.now() + 3600 * 1e3).toISOString()
              // V4: 增加1小时的过期时间
            );
          } else {
            response = yield api_traderequest.basisWash(
              setterID.value,
              instrumentId.value,
              quantity,
              price,
              "MANUAL",
              // V4: 增加执行模式
              new Date(Date.now() + 3600 * 1e3).toISOString()
              // V4: 增加1小时的过期时间
            );
          }
          if (response.code === 0) {
            const typeText = isPointPrice.value ? "点价" : "洗基差";
            utils_toast.toast.success(`${typeText}请求已提交`);
            if (tradeFormRef.value) {
              tradeFormRef.value.resetForm();
            }
            loadTodayTradeRequests();
          } else {
            utils_toast.toast.error(response.msg || "提交失败");
          }
        } catch (error) {
          console.error("提交交易请求失败:", error);
          utils_toast.toast.error("网络错误，请稍后重试");
        } finally {
          loading.value = false;
        }
      });
    }
    function switchTab(newRequestType) {
      console.log("切换 Tab:", newRequestType);
      requestType.value = newRequestType;
      if (tradeFormRef.value) {
        tradeFormRef.value.resetForm();
      }
      loadTodayTradeRequests();
    }
    function handlePageParams(options) {
      console.log("处理页面参数:", options);
      if (tradeFormRef.value) {
        tradeFormRef.value.resetForm();
      }
      resetContractData();
      todayTradeRequests.value = [];
      if (options == null ? void 0 : options.requestType) {
        requestType.value = options.requestType;
        console.log("设置请求类型:", requestType.value);
      } else {
        requestType.value = "PointPrice";
      }
      if (options == null ? void 0 : options.instrumentId) {
        const newInstrumentId = parseInt(options.instrumentId, 10);
        instrumentId.value = newInstrumentId;
        loadInstrument(instrumentId.value);
        loadTodayTradeRequests();
      }
      if (options == null ? void 0 : options.setterID) {
        const newSetterID = parseInt(options.setterID, 10);
        setterID.value = newSetterID;
      }
      if (options == null ? void 0 : options.setter) {
        setterName.value = options.setter;
      }
      loadAllAvailableContracts();
    }
    common_vendor.onLoad((options) => {
      handlePageParams(options);
    });
    common_vendor.onShow(() => {
      console.log("execute page onShow");
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      if (currentPage && currentPage.options) {
        console.log("onShow 重新处理参数:", currentPage.options);
        const newInstrumentId = currentPage.options.instrumentId ? parseInt(currentPage.options.instrumentId, 10) : null;
        const newSetterID = currentPage.options.setterID ? parseInt(currentPage.options.setterID, 10) : null;
        const newTimestamp = currentPage.options._t;
        const paramsChanged = newInstrumentId !== instrumentId.value || newSetterID !== setterID.value || currentPage.options.setter !== setterName.value || newTimestamp;
        if (paramsChanged) {
          console.log("参数发生变化或新的跳转，重新加载数据");
          handlePageParams(currentPage.options);
        }
      }
    });
    return (_ctx, _cache) => {
      var _a, _b;
      return {
        a: common_vendor.p({
          title: "交易执行",
          fixed: false
        }),
        b: requestType.value === "PointPrice" ? 1 : "",
        c: common_vendor.o(($event) => switchTab("PointPrice")),
        d: requestType.value === "BasisWash" ? 1 : "",
        e: common_vendor.o(($event) => switchTab("BasisWash")),
        f: common_vendor.o(common_vendor.unref(handleCombinationChange)),
        g: common_vendor.o(($event) => common_vendor.isRef(pickerValue) ? pickerValue.value = $event : null),
        h: common_vendor.p({
          combinations: common_vendor.unref(availableCombinations),
          ["current-display"]: common_vendor.unref(currentCombinationDisplay),
          modelValue: common_vendor.unref(pickerValue)
        }),
        i: common_vendor.t(currentContractStats.value.remainingQuantity),
        j: common_vendor.t(isPointPrice.value ? "基差" : "固价"),
        k: common_vendor.t(currentContractStats.value.frozenQuantity),
        l: common_vendor.t(isPointPrice.value ? "基差" : "固价"),
        m: common_vendor.t(currentContractStats.value.availableQuantity),
        n: common_vendor.t(isPointPrice.value ? "基差" : "固价"),
        o: common_vendor.t(currentContractStats.value.weightedPrice.toFixed(2)),
        p: common_vendor.t(isPointPrice.value ? "基差" : "固价"),
        q: common_vendor.o(handlePriceClick),
        r: common_vendor.p({
          ["instrument-id"]: ((_a = common_vendor.unref(instrument)) == null ? void 0 : _a.instrument_id) || "",
          symbol: ((_b = common_vendor.unref(instrument)) == null ? void 0 : _b.instrument_id) || "i2510",
          exchange: "DCE"
        }),
        s: common_vendor.sr(tradeFormRef, "456eb489-4,456eb489-0", {
          "k": "tradeFormRef"
        }),
        t: common_vendor.o((data) => handleTradeRequestSubmit(data)),
        v: common_vendor.p({
          ["is-point-price"]: isPointPrice.value,
          ["available-quantity"]: currentContractStats.value.availableQuantity,
          ["weighted-price"]: currentContractStats.value.weightedPrice,
          ["limit-up-price"]: limitUpPrice.value,
          ["limit-down-price"]: limitDownPrice.value,
          loading: loading.value
        }),
        w: common_vendor.p({
          requests: todayTradeRequests.value,
          ["request-type"]: requestType.value,
          loading: loading.value,
          mode: "viewer"
        })
      };
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-456eb489"]]);
wx.createPage(MiniProgramPage);
