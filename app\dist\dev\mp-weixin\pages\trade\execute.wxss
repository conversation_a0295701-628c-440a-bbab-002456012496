/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.trade-execute-page.data-v-456eb489 {
  background-color: #f7f8fa;
}
.tab-container.data-v-456eb489 {
  display: flex;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  padding: 8rpx;
  margin: 16rpx;
}
.tab-container .tab-item.data-v-456eb489 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 16rpx;
  border-radius: 6rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}
.tab-container .tab-item .tab-text.data-v-456eb489 {
  font-size: 28rpx;
  font-weight: 500;
  color: #606266;
  transition: color 0.3s ease;
}
.tab-container .tab-item.active.data-v-456eb489 {
  background-color: white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.tab-container .tab-item.active .tab-text.data-v-456eb489 {
  color: #409eff;
  font-weight: 600;
}
.tab-container .tab-item.data-v-456eb489:hover:not(.active) {
  background-color: rgba(255, 255, 255, 0.5);
}
.stats-grid .stat-item.data-v-456eb489 {
  min-height: 120rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 12rpx;
  padding: 20rpx 12rpx;
  transition: all 0.3s ease;
}
.stats-grid .stat-item.data-v-456eb489:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
}
.stats-grid .stat-item .stat-number.data-v-456eb489 {
  font-size: 36rpx;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 8rpx;
}
.stats-grid .stat-item .stat-label.data-v-456eb489 {
  font-size: 22rpx;
  color: #909399;
  text-align: center;
  line-height: 1.2;
}