"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../common/vendor.js");
const store_user = require("./user.js");
const utils_toast = require("../utils/toast.js");
var define_import_meta_env_default = {};
const VITE_WS_URL = define_import_meta_env_default.VITE_WS_URL || "ws://localhost:8888/ws/app";
const useSocketStore = common_vendor.defineStore("socket", () => {
  const socket = common_vendor.ref(null);
  const isConnected = common_vendor.ref(false);
  const isAuthenticated = common_vendor.ref(false);
  let heartbeatInterval = null;
  let reconnectTimeout = null;
  let reconnectAttempts = 0;
  const eventHandlers = /* @__PURE__ */ new Map();
  function registerHandler(eventType, handler) {
    if (!eventHandlers.has(eventType)) {
      eventHandlers.set(eventType, []);
    }
    const handlers = eventHandlers.get(eventType);
    if (!handlers.includes(handler)) {
      handlers.push(handler);
      console.log(`[WebSocket] 注册事件处理器: ${eventType}`);
    }
  }
  function unregisterHandler(eventType, handler) {
    const handlers = eventHandlers.get(eventType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
        console.log(`[WebSocket] 取消注册事件处理器: ${eventType}`);
        if (handlers.length === 0) {
          eventHandlers.delete(eventType);
        }
      }
    }
  }
  function connect() {
    if (socket.value && socket.value.readyState === WebSocket.OPEN) {
      console.log("[WebSocket] 已连接，无需重复连接。");
      return;
    }
    console.log(`[WebSocket] 正在连接到 ${VITE_WS_URL}...`);
    const ws = new WebSocket(VITE_WS_URL);
    ws.onopen = handleOpen;
    ws.onmessage = handleMessage;
    ws.onclose = handleClose;
    ws.onerror = handleError;
    socket.value = ws;
  }
  function disconnect() {
    if (socket.value) {
      console.log("[WebSocket] 主动断开连接。");
      socket.value.close();
    }
    stopHeartbeat();
    if (reconnectTimeout)
      clearTimeout(reconnectTimeout);
  }
  function sendMessage(event, payload) {
    if (!socket.value || socket.value.readyState !== WebSocket.OPEN) {
      utils_toast.toast.error("连接已断开，消息发送失败");
      return;
    }
    const message = {
      event,
      payload,
      timestamp: Date.now()
    };
    console.log("[WebSocket] 发送消息:", message);
    socket.value.send(JSON.stringify(message));
  }
  function handleOpen() {
    console.log("[WebSocket] 连接成功！");
    isConnected.value = true;
    reconnectAttempts = 0;
    if (reconnectTimeout)
      clearTimeout(reconnectTimeout);
    startHeartbeat();
    const userStore = store_user.useUserStore();
    if (userStore.isLoggedIn) {
      authenticate(userStore.token);
    }
  }
  function handleMessage(event) {
    return __async(this, null, function* () {
      var _a, _b, _c, _d;
      try {
        const message = JSON.parse(event.data);
        if (message.event !== "pong") {
          console.log("[WebSocket] 收到消息:", message);
        }
        switch (message.event) {
          case "pong":
            if ((_a = message.payload) == null ? void 0 : _a.timestamp) {
              const rtt = Date.now() - message.payload.timestamp;
              console.log(`[WebSocket] Pong received. RTT: ${rtt}ms`);
            }
            break;
          case "auth_response":
            if ((_b = message.payload) == null ? void 0 : _b.success) {
              isAuthenticated.value = true;
              console.log("[WebSocket] 认证成功。");
            } else {
              utils_toast.toast.error(`认证失败: ${(_c = message.payload) == null ? void 0 : _c.message}`);
            }
            break;
          case "notification":
            utils_toast.toast.info(((_d = message.payload) == null ? void 0 : _d.content) || "收到一条新通知");
            break;
          case "trade_update":
            console.log("[WebSocket] 交易更新:", message.payload);
            utils_toast.toast.success("您的交易状态已更新");
            break;
        }
        const handlers = eventHandlers.get(message.event);
        if (handlers && handlers.length > 0) {
          handlers.forEach((handler) => {
            try {
              handler(message.payload);
            } catch (error) {
              console.error(`[WebSocket] 事件处理器执行失败 (${message.event}):`, error);
            }
          });
        }
      } catch (error) {
        console.error("[WebSocket] 解析消息失败:", error);
      }
    });
  }
  function handleClose() {
    console.log("[WebSocket] 连接已关闭。");
    isConnected.value = false;
    isAuthenticated.value = false;
    stopHeartbeat();
    attemptReconnect();
  }
  function handleError(event) {
    console.error("[WebSocket] 发生错误:", event);
  }
  function startHeartbeat() {
    stopHeartbeat();
    heartbeatInterval = setInterval(() => {
      sendMessage("ping", { timestamp: Date.now() });
    }, 25e3);
  }
  function stopHeartbeat() {
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval);
      heartbeatInterval = null;
    }
  }
  function attemptReconnect() {
    if (reconnectAttempts >= 5) {
      console.log("[WebSocket] 重连次数过多，已停止。");
      return;
    }
    const delay = Math.pow(2, reconnectAttempts) * 1e3;
    reconnectAttempts++;
    console.log(`[WebSocket] 将在 ${delay / 1e3} 秒后尝试重新连接...`);
    reconnectTimeout = setTimeout(() => {
      connect();
    }, delay);
  }
  function authenticate(token) {
    sendMessage("auth", { token });
  }
  function subscribe(channel) {
    sendMessage("subscribe", { channel });
  }
  return {
    isConnected: common_vendor.readonly(isConnected),
    isAuthenticated: common_vendor.readonly(isAuthenticated),
    connect,
    disconnect,
    sendMessage,
    authenticate,
    subscribe,
    registerHandler,
    unregisterHandler
  };
});
exports.useSocketStore = useSocketStore;
