"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __objRest = (source, exclude) => {
  var target = {};
  for (var prop in source)
    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)
      target[prop] = source[prop];
  if (source != null && __getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(source)) {
      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))
        target[prop] = source[prop];
    }
  return target;
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const api_contract = require("../../api/contract.js");
if (!Array) {
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  const _easycom_wd_radio2 = common_vendor.resolveComponent("wd-radio");
  const _easycom_wd_radio_group2 = common_vendor.resolveComponent("wd-radio-group");
  const _easycom_wd_textarea2 = common_vendor.resolveComponent("wd-textarea");
  const _easycom_wd_form2 = common_vendor.resolveComponent("wd-form");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_input2 + _easycom_wd_radio2 + _easycom_wd_radio_group2 + _easycom_wd_textarea2 + _easycom_wd_form2 + _easycom_wd_button2 + _component_layout_default_uni)();
}
const _easycom_wd_input = () => "../../node-modules/wot-design-uni/components/wd-input/wd-input.js";
const _easycom_wd_radio = () => "../../node-modules/wot-design-uni/components/wd-radio/wd-radio.js";
const _easycom_wd_radio_group = () => "../../node-modules/wot-design-uni/components/wd-radio-group/wd-radio-group.js";
const _easycom_wd_textarea = () => "../../node-modules/wot-design-uni/components/wd-textarea/wd-textarea.js";
const _easycom_wd_form = () => "../../node-modules/wot-design-uni/components/wd-form/wd-form.js";
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
if (!Math) {
  (UserSelector + InstrumentSelector + _easycom_wd_input + _easycom_wd_radio + _easycom_wd_radio_group + _easycom_wd_textarea + _easycom_wd_form + _easycom_wd_button)();
}
const InstrumentSelector = () => "../../components/InstrumentSelector.js";
const UserSelector = () => "../../components/UserSelector.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "form",
  setup(__props) {
    const router = common_vendor.useRouter();
    const route = common_vendor.useRoute();
    const formRef = common_vendor.ref();
    const isEdit = common_vendor.ref(false);
    const contractId = common_vendor.ref(0);
    const submitLoading = common_vendor.ref(false);
    const formData = common_vendor.reactive({
      contractCode: "",
      pricerID: 0,
      instrumentRefID: 0,
      instrument: null,
      remarks: "",
      totalQuantity: 0,
      priceType: "basis",
      priceValue: 0
    });
    const selectedPricer = common_vendor.ref(null);
    const formRules = common_vendor.reactive({
      contractCode: [
        { required: true, message: "请输入合同编码" }
      ],
      totalQuantity: [
        { required: true, message: "请输入合同总数量", trigger: "blur" },
        { pattern: /^(0|[1-9]\d*)$/, message: "数量必须为非负整数", trigger: "blur" }
      ],
      priceValue: [
        { required: true, message: "请输入价格/基差值", trigger: "blur" }
      ]
    });
    function loadContractDetail() {
      return __async(this, null, function* () {
        if (!contractId.value)
          return;
        try {
          const response = yield api_contract.getContractDetail(contractId.value);
          if (response.code === 0) {
            const contract = response.data;
            if (contract.status !== "Unexecuted") {
              common_vendor.index.showToast({
                title: "只有未执行状态的合同才能编辑",
                icon: "error"
              });
              setTimeout(() => {
                router.back();
              }, 1500);
              return;
            }
            let priceType = "basis";
            if (typeof contract.priceType === "string") {
              priceType = contract.priceType;
            } else if (typeof contract.priceType === "object" && contract.priceType && "value" in contract.priceType) {
              priceType = contract.priceType.value;
            }
            Object.assign(formData, {
              contractCode: contract.contractCode,
              pricerID: contract.pricerID,
              instrumentRefID: contract.instrumentRefID,
              instrument: contract.instrument || null,
              remarks: contract.remarks,
              totalQuantity: contract.totalQuantity,
              priceType,
              priceValue: contract.priceValue
            });
            selectedPricer.value = contract.pricer;
            if (contract.pricer) {
              onUserChange(contract.pricer);
            }
          } else {
            common_vendor.index.showToast({
              title: response.msg || "获取合同详情失败",
              icon: "error"
            });
          }
        } catch (error) {
          console.error("获取合同详情失败:", error);
          common_vendor.index.showToast({
            title: "网络错误",
            icon: "error"
          });
        }
      });
    }
    function onPriceTypeChange(value) {
      let newPriceType;
      if (typeof value === "string") {
        newPriceType = value;
      } else if (typeof value === "object" && value && "value" in value) {
        newPriceType = value.value;
      } else {
        newPriceType = "basis";
      }
      formData.priceType = newPriceType;
    }
    function onUserChange(user) {
      selectedPricer.value = user;
      formData.pricerID = user ? user.ID : 0;
    }
    function onInstrumentChange(selectedInstrumentId, instrumentData) {
      formData.instrumentRefID = selectedInstrumentId || 0;
      formData.instrument = instrumentData || null;
    }
    function submitForm() {
      return __async(this, null, function* () {
        const valid = yield formRef.value.validate();
        if (!valid.valid)
          return;
        if (!formData.instrumentRefID) {
          common_vendor.index.showToast({
            title: "请选择期货合约",
            icon: "error"
          });
          return;
        }
        if (!formData.totalQuantity || formData.totalQuantity <= 0) {
          common_vendor.index.showToast({
            title: "请输入有效的合同数量",
            icon: "error"
          });
          return;
        }
        if (!formData.priceValue || formData.priceValue === 0) {
          common_vendor.index.showToast({
            title: "请输入有效的价格/基差值",
            icon: "error"
          });
          return;
        }
        if (!formData.pricerID) {
          common_vendor.index.showToast({
            title: "请选择点价方",
            icon: "error"
          });
          return;
        }
        submitLoading.value = true;
        try {
          let response;
          const _a = formData, { instrument } = _a, submitData = __objRest(_a, ["instrument"]);
          if (isEdit.value) {
            const updateData = __spreadValues({
              id: contractId.value
            }, submitData);
            response = yield api_contract.updateContract(updateData);
          } else {
            response = yield api_contract.createContract(submitData);
          }
          if (response.code === 0) {
            common_vendor.index.showToast({
              title: isEdit.value ? "更新成功" : "创建成功",
              icon: "success"
            });
            setTimeout(() => {
              router.back();
            }, 1500);
          } else {
            common_vendor.index.showToast({
              title: response.msg || (isEdit.value ? "更新失败" : "创建失败"),
              icon: "error"
            });
          }
        } catch (error) {
          console.error("提交合同失败:", error);
          common_vendor.index.showToast({
            title: "网络错误",
            icon: "error"
          });
        } finally {
          submitLoading.value = false;
        }
      });
    }
    function goBack() {
      router.back();
    }
    common_vendor.onMounted(() => {
      const id = route.query.id;
      if (id) {
        contractId.value = Number(id);
        isEdit.value = true;
        loadContractDetail();
      }
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.t(isEdit.value ? "编辑合同" : "创建合同"),
        b: common_vendor.o(onUserChange),
        c: common_vendor.o(($event) => formData.pricerID = $event),
        d: common_vendor.p({
          label: "选择点价方",
          placeholder: "暂未选择点价方",
          modelValue: formData.pricerID
        }),
        e: common_vendor.o(onInstrumentChange),
        f: common_vendor.o(($event) => formData.instrumentRefID = $event),
        g: common_vendor.p({
          label: "期货合约",
          placeholder: "请输入期货合约",
          ["custom-class"]: "dj-form-field",
          ["custom-label-class"]: "dj-form-label",
          ["custom-value-class"]: "dj-form-input",
          modelValue: formData.instrumentRefID
        }),
        h: common_vendor.o(($event) => formData.contractCode = $event),
        i: common_vendor.p({
          label: "合同编码",
          placeholder: "请输入合同编码",
          required: true,
          prop: "contractCode",
          ["custom-class"]: "dj-form-field",
          ["custom-label-class"]: "dj-form-label",
          ["custom-input-class"]: "dj-form-input",
          modelValue: formData.contractCode
        }),
        j: common_vendor.o(common_vendor.m(($event) => formData.totalQuantity = $event, {
          number: true
        }, true)),
        k: common_vendor.p({
          label: "合同总数量",
          type: "number",
          placeholder: "请输入合同总数量",
          prop: "totalQuantity",
          ["custom-class"]: "dj-form-field",
          ["custom-label-class"]: "dj-form-label",
          ["custom-input-class"]: "dj-form-input",
          modelValue: formData.totalQuantity
        }),
        l: common_vendor.p({
          value: "basis"
        }),
        m: common_vendor.p({
          value: "fixed"
        }),
        n: common_vendor.o(onPriceTypeChange),
        o: common_vendor.o(($event) => formData.priceType = $event),
        p: common_vendor.p({
          modelValue: formData.priceType
        }),
        q: common_vendor.o(common_vendor.m(($event) => formData.priceValue = $event, {
          number: true
        }, true)),
        r: common_vendor.p({
          label: formData.priceType === "basis" ? "基差值" : "固定价格",
          type: "number",
          placeholder: formData.priceType === "basis" ? "请输入基差值" : "请输入固定价格",
          prop: "priceValue",
          ["custom-class"]: "dj-form-field",
          ["custom-label-class"]: "dj-form-label",
          ["custom-input-class"]: "dj-form-input",
          modelValue: formData.priceValue
        }),
        s: common_vendor.o(($event) => formData.remarks = $event),
        t: common_vendor.p({
          label: "备注",
          placeholder: "请输入备注信息",
          maxlength: 500,
          ["auto-height"]: true,
          ["custom-class"]: "dj-form-textarea",
          ["custom-label-class"]: "dj-form-label",
          ["custom-textarea-class"]: "dj-form-input",
          ["custom-textarea-container-class"]: "dj-form-textarea-container",
          modelValue: formData.remarks
        }),
        v: common_vendor.sr(formRef, "8bc4e71f-1,8bc4e71f-0", {
          "k": "formRef"
        }),
        w: common_vendor.p({
          model: formData,
          rules: formRules
        }),
        x: common_vendor.o(goBack),
        y: common_vendor.p({
          type: "info",
          ["custom-class"]: "dj-btn-secondary"
        }),
        z: common_vendor.t(isEdit.value ? "更新合同" : "创建合同"),
        A: common_vendor.o(submitForm),
        B: common_vendor.p({
          type: "primary",
          ["custom-class"]: "dj-btn-primary",
          loading: submitLoading.value
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-8bc4e71f"]]);
wx.createPage(MiniProgramPage);
