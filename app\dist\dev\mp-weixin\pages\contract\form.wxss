/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.contract-form-page.data-v-8bc4e71f {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 120rpx;
}
.page-header.data-v-8bc4e71f {
  padding: 30rpx 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}
.page-header .page-title.data-v-8bc4e71f {
  font-size: 40rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.form-container.data-v-8bc4e71f {
  padding: 20rpx;
}
.form-section.data-v-8bc4e71f {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}
.form-section .section-title.data-v-8bc4e71f {
  font-size: 36rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 30rpx;
  padding-bottom: 16rpx;
  position: relative;
}
.form-section .section-title.data-v-8bc4e71f::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 2rpx;
}
.price-type-selector.data-v-8bc4e71f {
  margin-bottom: 30rpx;
}
.price-type-selector .selector-label.data-v-8bc4e71f {
  display: block;
  font-size: 32rpx;
  color: #303133;
  font-weight: 500;
  margin-bottom: 20rpx;
}
.price-type-selector .price-type-tabs.data-v-8bc4e71f {
  background-color: #f5f7fa;
  border-radius: 12rpx;
  padding: 8rpx;
  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.06);
}
.price-type-selector .price-type-tabs.data-v-8bc4e71f .wd-radio-group {
  display: flex;
  flex-direction: row;
  gap: 0;
  width: 100%;
}
.price-type-selector .price-type-tabs.data-v-8bc4e71f .wd-radio {
  flex: 1;
  margin-bottom: 0;
  position: relative;
}
.price-type-selector .price-type-tabs.data-v-8bc4e71f .wd-radio .wd-radio__input {
  display: none;
}
.price-type-selector .price-type-tabs.data-v-8bc4e71f .wd-radio .wd-radio__label {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 16rpx;
  border-radius: 8rpx;
  background-color: transparent;
  color: #606266;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 80rpx;
  box-sizing: border-box;
  border: 2rpx solid transparent;
}
.price-type-selector .price-type-tabs.data-v-8bc4e71f .wd-radio .wd-radio__label:hover {
  color: #667eea;
  background-color: rgba(102, 126, 234, 0.08);
}
.price-type-selector .price-type-tabs.data-v-8bc4e71f .wd-radio.is-checked .wd-radio__label {
  background-color: white;
  color: #667eea;
  font-weight: 600;
  border-color: rgba(102, 126, 234, 0.2);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.15), 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
  transform: translateY(-1rpx);
}
.price-type-selector .price-type-tabs.data-v-8bc4e71f .wd-radio .wd-radio__shape {
  display: none;
}
.form-actions.data-v-8bc4e71f {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background: white;
  display: flex;
  gap: 20rpx;
  z-index: 10;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.form-actions.data-v-8bc4e71f .wd-button {
  flex: 1;
}
.data-v-8bc4e71f  .dj-form-field,.data-v-8bc4e71f  .dj-form-textarea {
  background: white;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.data-v-8bc4e71f  .dj-form-field:hover,.data-v-8bc4e71f  .dj-form-textarea:hover {
  background-color: #f9fafc;
}
.data-v-8bc4e71f  .dj-form-field:focus-within,.data-v-8bc4e71f  .dj-form-textarea:focus-within {
  background-color: #f9fafc;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}
.data-v-8bc4e71f  .dj-form-field.is-disabled,.data-v-8bc4e71f  .dj-form-textarea.is-disabled {
  background-color: #f5f7fa;
  cursor: not-allowed;
}
.data-v-8bc4e71f  .dj-form-field::before,.data-v-8bc4e71f  .dj-form-textarea::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: linear-gradient(180deg, #667eea, #764ba2);
}
.data-v-8bc4e71f  .dj-form-label {
  color: #303133;
  text-align: right;
  justify-content: flex-end;
  width: 200rpx;
  padding-right: 24rpx;
  font-size: 28rpx;
}
.data-v-8bc4e71f  .dj-form-input {
  color: #303133;
  font-size: 28rpx;
}
.data-v-8bc4e71f  .dj-form-input.is-placeholder {
  color: #909399;
  font-style: italic;
}
.data-v-8bc4e71f  .dj-form-input:focus {
  color: #667eea;
}
.data-v-8bc4e71f  .dj-form-textarea-container {
  width: 100%;
}
.data-v-8bc4e71f  .dj-btn-danger,.data-v-8bc4e71f  .dj-btn-secondary,.data-v-8bc4e71f  .dj-btn-primary {
  border-radius: 8rpx;
  font-weight: 500;
  font-size: 32rpx;
  transition: all 0.3s ease;
  padding: 20rpx;
}
.data-v-8bc4e71f  .dj-btn-danger:hover,.data-v-8bc4e71f  .dj-btn-secondary:hover,.data-v-8bc4e71f  .dj-btn-primary:hover {
  opacity: 0.9;
}
.data-v-8bc4e71f  .dj-btn-danger:active,.data-v-8bc4e71f  .dj-btn-secondary:active,.data-v-8bc4e71f  .dj-btn-primary:active {
  opacity: 1;
}
.data-v-8bc4e71f  .dj-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.2);
}
.data-v-8bc4e71f  .dj-btn-primary:hover {
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  opacity: 0.95;
}
.data-v-8bc4e71f  .dj-btn-primary:active {
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.2);
  opacity: 1;
}
.data-v-8bc4e71f  .dj-btn-primary.is-loading {
  background: linear-gradient(135deg, #a5b0f3 0%, #a586c0 100%);
  opacity: 0.8;
}
.data-v-8bc4e71f  .dj-btn-secondary {
  background: white;
  color: #606266;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}
.data-v-8bc4e71f  .dj-btn-secondary:hover {
  color: #303133;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}
.data-v-8bc4e71f  .dj-btn-secondary:active {
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}
.data-v-8bc4e71f  .dj-btn-danger {
  background: linear-gradient(135deg, #f56c6c 0%, #e64242 100%);
  border: none;
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(245, 108, 108, 0.2);
}
.data-v-8bc4e71f  .dj-btn-danger:hover {
  box-shadow: 0 4rpx 12rpx rgba(245, 108, 108, 0.3);
  opacity: 0.95;
}
.data-v-8bc4e71f  .dj-btn-danger:active {
  box-shadow: 0 2rpx 8rpx rgba(245, 108, 108, 0.2);
  opacity: 1;
}
.data-v-8bc4e71f  .dj-radio .wd-checkbox__label,.data-v-8bc4e71f  .dj-checkbox .wd-checkbox__label,.data-v-8bc4e71f  .dj-radio .wd-radio__label,.data-v-8bc4e71f  .dj-checkbox .wd-radio__label {
  color: #303133;
  font-size: 28rpx;
  font-weight: 500;
  padding-left: 12rpx;
}
.data-v-8bc4e71f  .dj-radio .wd-checkbox__shape,.data-v-8bc4e71f  .dj-checkbox .wd-checkbox__shape,.data-v-8bc4e71f  .dj-radio .wd-radio__shape,.data-v-8bc4e71f  .dj-checkbox .wd-radio__shape {
  border-color: #c0c4cc;
  transition: all 0.3s ease;
}
.data-v-8bc4e71f  .dj-radio .wd-checkbox__shape.is-checked,.data-v-8bc4e71f  .dj-checkbox .wd-checkbox__shape.is-checked,.data-v-8bc4e71f  .dj-radio .wd-radio__shape.is-checked,.data-v-8bc4e71f  .dj-checkbox .wd-radio__shape.is-checked {
  background-color: #667eea;
  border-color: #667eea;
  box-shadow: 0 0 4rpx rgba(102, 126, 234, 0.3);
}
.data-v-8bc4e71f  .dj-checkbox .wd-checkbox__shape {
  border-radius: 4rpx;
}
.data-v-8bc4e71f  .dj-search {
  background: white;
  border-radius: 40rpx;
  padding: 0 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.data-v-8bc4e71f  .dj-search:focus-within {
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}
.data-v-8bc4e71f  .dj-search .wd-search__input {
  background: transparent;
  color: #303133;
  font-size: 28rpx;
  height: 80rpx;
}
.data-v-8bc4e71f  .dj-search .wd-search__icon {
  color: #667eea;
}
.data-v-8bc4e71f  .dj-search .wd-search__placeholder {
  color: #909399;
  font-size: 28rpx;
}
.data-v-8bc4e71f  .dj-popup .wd-popup__container {
  border-radius: 16rpx 16rpx 0 0;
  background: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}