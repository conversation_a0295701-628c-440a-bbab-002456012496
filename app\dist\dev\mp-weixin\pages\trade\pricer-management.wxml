<layout-default-uni class="data-v-732d0900" u-s="{{['d']}}" u-i="732d0900-0" bind:__l="__l"><view class="pricer-management-page data-v-732d0900"><view wx:if="{{a}}" class="refresh-indicator bg-blue-500 text-white text-center py-2 mb-3 rounded mx-3 data-v-732d0900"> 正在刷新数据... </view><view class="filter-bar flex items-center bg-white rounded-lg shadow-sm px-3 py-2 mb-3 mx-3 data-v-732d0900"><wd-select-picker wx:if="{{e}}" u-s="{{['d']}}" bindchange="{{c}}" class="flex-1 data-v-732d0900" u-i="732d0900-1,732d0900-0" bind:__l="__l" bindupdateModelValue="{{d}}" u-p="{{e}}"><view class="status-display data-v-732d0900"><text class="text-sm text-gray-600 mr-2 data-v-732d0900">状态:</text><text class="status-text data-v-732d0900">{{b}}</text></view></wd-select-picker></view><view class="px-3 data-v-732d0900"><trade-request-list wx:if="{{h}}" class="data-v-732d0900" bindcancel="{{f}}" bindrefresh="{{g}}" u-i="732d0900-2,732d0900-0" bind:__l="__l" u-p="{{h}}"/></view><view wx:if="{{i}}" class="load-more-tip text-center py-4 data-v-732d0900"><view wx:if="{{j}}" class="flex justify-center items-center data-v-732d0900"><view class="loading-spinner w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2 data-v-732d0900"></view><text class="text-sm text-gray-500 data-v-732d0900">{{k}}</text></view><text wx:else class="text-xs text-gray-400 data-v-732d0900">{{l}}</text></view></view></layout-default-uni>